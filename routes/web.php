<?php

use App\Livewire\Admin\Dashboard;
use App\Livewire\Settings\Appearance;
use App\Livewire\Settings\Password;
use App\Livewire\Settings\Profile;
use Illuminate\Support\Facades\Route;

// Frontend Routes
Route::get('/', [App\Http\Controllers\HomeController::class, 'index'])->name('home');
Route::get('/search', [App\Http\Controllers\HomeController::class, 'search'])->name('search');
Route::get('/about', [App\Http\Controllers\HomeController::class, 'about'])->name('about');
Route::get('/contact', [App\Http\Controllers\HomeController::class, 'contact'])->name('contact');
Route::post('/contact', [App\Http\Controllers\HomeController::class, 'contactSubmit'])->name('contact.submit');

// Article Routes
Route::prefix('articles')->name('articles.')->group(function () {
    Route::get('/', [App\Http\Controllers\ArticleController::class, 'index'])->name('index');
    Route::get('/{article:slug}', [App\Http\Controllers\ArticleController::class, 'show'])->name('show');
    Route::post('/{article:slug}/comments', [App\Http\Controllers\ArticleController::class, 'storeComment'])->name('comments.store');
    Route::post('/comments/{comment}/reaction', [App\Http\Controllers\ArticleController::class, 'commentReaction'])->name('comments.reaction');
    Route::post('/comments/{comment}/report', [App\Http\Controllers\ArticleController::class, 'reportComment'])->name('comments.report');
});

// Category Routes
Route::prefix('categories')->name('categories.')->group(function () {
    Route::get('/', [App\Http\Controllers\CategoryController::class, 'index'])->name('index');
    Route::get('/hierarchy', [App\Http\Controllers\CategoryController::class, 'hierarchy'])->name('hierarchy');
    Route::get('/{category:slug}', [App\Http\Controllers\CategoryController::class, 'show'])->name('show');
    Route::get('/{category:slug}/search', [App\Http\Controllers\CategoryController::class, 'search'])->name('search');
});

// Subscription Routes
Route::prefix('subscriptions')->name('subscriptions.')->group(function () {
    Route::get('/', [App\Http\Controllers\SubscriptionController::class, 'index'])->name('index');
    Route::get('/plans/{plan}/checkout', [App\Http\Controllers\SubscriptionController::class, 'checkout'])->name('checkout');
    Route::post('/plans/{plan}/payment', [App\Http\Controllers\SubscriptionController::class, 'processPayment'])->name('payment');
    Route::get('/payment/success/{transactionId}', [App\Http\Controllers\SubscriptionController::class, 'paymentSuccess'])->name('payment.success');
    Route::get('/payment/failed/{transactionId}', [App\Http\Controllers\SubscriptionController::class, 'paymentFailed'])->name('payment.failed');
    Route::post('/discount/apply', [App\Http\Controllers\SubscriptionController::class, 'applyDiscount'])->name('discount.apply');

    Route::middleware('auth')->group(function () {
        Route::get('/manage', [App\Http\Controllers\SubscriptionController::class, 'manage'])->name('manage');
        Route::post('/cancel', [App\Http\Controllers\SubscriptionController::class, 'cancel'])->name('cancel');
    });
});

// Advertisement Click Tracking
Route::get('/ad/click/{ad}', [App\Http\Controllers\HomeController::class, 'adClick'])->name('ad.click');

// Role-based dashboard redirects
Route::get('/dashboard', function () {
    $user = auth()->user();

    return match($user->role->value) {
        'admin' => redirect()->route('admin.dashboard'),
        'manager' => redirect()->route('manager.dashboard'),
        'editor' => redirect()->route('editor.dashboard'),
        'author' => redirect()->route('author.dashboard'),
        default => redirect()->route('user.dashboard'),
    };
})->middleware(['auth', 'verified'])->name('dashboard');

// User Dashboard
Route::get('/user/dashboard', App\Livewire\User\Dashboard::class)
    ->middleware(['auth', 'verified'])
    ->name('user.dashboard');

// Author Dashboard
Route::middleware(['auth', 'verified'])->prefix('author')->name('author.')->group(function () {
    Route::get('dashboard', App\Livewire\Author\Dashboard::class)->name('dashboard');
});

// Editor Dashboard
Route::middleware(['auth', 'verified'])->prefix('editor')->name('editor.')->group(function () {
    Route::get('dashboard', App\Livewire\Editor\Dashboard::class)->name('dashboard');
});

// Manager Dashboard
Route::middleware(['auth', 'verified'])->prefix('manager')->name('manager.')->group(function () {
    Route::get('dashboard', App\Livewire\Manager\Dashboard::class)->name('dashboard');
});

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Route::get('settings/profile', Profile::class)->name('settings.profile');
    Route::get('settings/password', Password::class)->name('settings.password');
    Route::get('settings/appearance', Appearance::class)->name('settings.appearance');
});


/*------------------------------------------
--------------------------------------------
All Admin Routes List
--------------------------------------------
--------------------------------------------*/

Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Dashboard - Using Livewire Component
    Route::get('dashboard', App\Livewire\Admin\Dashboard::class)->name('dashboard');

    // Articles Management
    Route::resource('articles', App\Http\Controllers\Admin\ArticleController::class);

    // Legal Categories Management
    Route::resource('categories', App\Http\Controllers\Admin\LegalCategoryController::class);
    Route::post('categories/update-order', [App\Http\Controllers\Admin\LegalCategoryController::class, 'updateOrder'])->name('categories.update-order');
    Route::patch('categories/{category}/toggle-status', [App\Http\Controllers\Admin\LegalCategoryController::class, 'toggleStatus'])->name('categories.toggle-status');
    Route::patch('categories/{category}/toggle-featured', [App\Http\Controllers\Admin\LegalCategoryController::class, 'toggleFeatured'])->name('categories.toggle-featured');

    // Legacy routes for existing functionality
    Route::get('add-user', function(){
        return view('livewire.admin.user');
    })->name('add-user');
});

require __DIR__.'/auth.php';
