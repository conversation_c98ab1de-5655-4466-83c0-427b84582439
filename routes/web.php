<?php

use App\Livewire\Admin\Dashboard;
use App\Livewire\Settings\Appearance;
use App\Livewire\Settings\Password;
use App\Livewire\Settings\Profile;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
})->name('home');

Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Route::get('settings/profile', Profile::class)->name('settings.profile');
    Route::get('settings/password', Password::class)->name('settings.password');
    Route::get('settings/appearance', Appearance::class)->name('settings.appearance');
});


/*------------------------------------------
--------------------------------------------
All Admin Routes List
--------------------------------------------
--------------------------------------------*/

Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Dashboard
    Route::get('dashboard', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');
    Route::get('dashboard/stats', [App\Http\Controllers\Admin\DashboardController::class, 'getStats'])->name('dashboard.stats');
    Route::get('dashboard/chart-data', [App\Http\Controllers\Admin\DashboardController::class, 'getArticleChartData'])->name('dashboard.chart-data');

    // Articles Management
    Route::resource('articles', App\Http\Controllers\Admin\ArticleController::class);

    // Legal Categories Management
    Route::resource('categories', App\Http\Controllers\Admin\LegalCategoryController::class);
    Route::post('categories/update-order', [App\Http\Controllers\Admin\LegalCategoryController::class, 'updateOrder'])->name('categories.update-order');
    Route::patch('categories/{category}/toggle-status', [App\Http\Controllers\Admin\LegalCategoryController::class, 'toggleStatus'])->name('categories.toggle-status');
    Route::patch('categories/{category}/toggle-featured', [App\Http\Controllers\Admin\LegalCategoryController::class, 'toggleFeatured'])->name('categories.toggle-featured');

    // Legacy routes for existing functionality
    Route::get('add-user', function(){
        return view('livewire.admin.user');
    })->name('add-user');
});

require __DIR__.'/auth.php';
