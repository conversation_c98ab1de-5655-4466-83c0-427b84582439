<?php

use App\Livewire\Admin\Dashboard;
use App\Livewire\Settings\Appearance;
use App\Livewire\Settings\Password;
use App\Livewire\Settings\Profile;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
})->name('home');

// Role-based dashboard redirects
Route::get('/dashboard', function () {
    $user = auth()->user();

    return match($user->role->value) {
        'admin' => redirect()->route('admin.dashboard'),
        'manager' => redirect()->route('manager.dashboard'),
        'editor' => redirect()->route('editor.dashboard'),
        'author' => redirect()->route('author.dashboard'),
        default => redirect()->route('user.dashboard'),
    };
})->middleware(['auth', 'verified'])->name('dashboard');

// User Dashboard
Route::get('/user/dashboard', App\Livewire\User\Dashboard::class)
    ->middleware(['auth', 'verified'])
    ->name('user.dashboard');

// Author Dashboard
Route::middleware(['auth', 'verified'])->prefix('author')->name('author.')->group(function () {
    Route::get('dashboard', App\Livewire\Author\Dashboard::class)->name('dashboard');
});

// Editor Dashboard
Route::middleware(['auth', 'verified'])->prefix('editor')->name('editor.')->group(function () {
    Route::get('dashboard', App\Livewire\Editor\Dashboard::class)->name('dashboard');
});

// Manager Dashboard
Route::middleware(['auth', 'verified'])->prefix('manager')->name('manager.')->group(function () {
    Route::get('dashboard', App\Livewire\Manager\Dashboard::class)->name('dashboard');
});

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Route::get('settings/profile', Profile::class)->name('settings.profile');
    Route::get('settings/password', Password::class)->name('settings.password');
    Route::get('settings/appearance', Appearance::class)->name('settings.appearance');
});


/*------------------------------------------
--------------------------------------------
All Admin Routes List
--------------------------------------------
--------------------------------------------*/

Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Dashboard - Using Livewire Component
    Route::get('dashboard', App\Livewire\Admin\Dashboard::class)->name('dashboard');

    // Articles Management
    Route::resource('articles', App\Http\Controllers\Admin\ArticleController::class);

    // Legal Categories Management
    Route::resource('categories', App\Http\Controllers\Admin\LegalCategoryController::class);
    Route::post('categories/update-order', [App\Http\Controllers\Admin\LegalCategoryController::class, 'updateOrder'])->name('categories.update-order');
    Route::patch('categories/{category}/toggle-status', [App\Http\Controllers\Admin\LegalCategoryController::class, 'toggleStatus'])->name('categories.toggle-status');
    Route::patch('categories/{category}/toggle-featured', [App\Http\Controllers\Admin\LegalCategoryController::class, 'toggleFeatured'])->name('categories.toggle-featured');

    // Legacy routes for existing functionality
    Route::get('add-user', function(){
        return view('livewire.admin.user');
    })->name('add-user');
});

require __DIR__.'/auth.php';
