<?php

namespace App\Livewire\Manager;

use App\Models\Article;
use App\Models\Comment;
use App\Models\LegalCategory;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class Dashboard extends Component
{
    public $stats = [];
    public $recentArticles;
    public $pendingComments;
    public $teamPerformance;
    public $categoryStats;

    public function mount()
    {
        $this->loadDashboardData();
    }

    public function loadDashboardData()
    {
        // Manager-specific statistics
        $this->stats = [
            'total_articles' => Article::count(),
            'published_articles' => Article::where('status', 'published')->count(),
            'pending_articles' => Article::where('status', 'draft')->count(),
            'pending_comments' => Comment::where('status', 'pending')->count(),
            'team_members' => User::whereIn('role', ['editor', 'author'])->count(),
            'categories_managed' => LegalCategory::where('is_active', true)->count(),
            'articles_this_month' => Article::whereMonth('created_at', now()->month)->count(),
            'comments_this_month' => Comment::whereMonth('created_at', now()->month)->count(),
        ];

        // Recent articles needing review
        $this->recentArticles = Article::with(['author', 'category'])
            ->where('status', 'draft')
            ->latest()
            ->limit(5)
            ->get();

        // Pending comments for moderation
        $this->pendingComments = Comment::with(['article', 'user'])
            ->where('status', 'pending')
            ->latest()
            ->limit(5)
            ->get();

        // Team performance
        $this->teamPerformance = User::whereIn('role', ['editor', 'author'])
            ->withCount(['authoredArticles as articles_count'])
            ->orderBy('articles_count', 'desc')
            ->limit(5)
            ->get();

        // Category statistics
        $this->categoryStats = LegalCategory::withCount('publishedArticles')
            ->orderBy('published_articles_count', 'desc')
            ->limit(5)
            ->get();
    }

    public function approveComment($commentId)
    {
        $comment = Comment::find($commentId);
        if ($comment) {
            $comment->approve(auth()->id());
            $this->loadDashboardData();
            $this->dispatch('comment-approved', ['message' => 'Comment approved successfully']);
        }
    }

    public function rejectComment($commentId)
    {
        $comment = Comment::find($commentId);
        if ($comment) {
            $comment->reject('Rejected by manager');
            $this->loadDashboardData();
            $this->dispatch('comment-rejected', ['message' => 'Comment rejected']);
        }
    }

    public function refreshStats()
    {
        $this->loadDashboardData();
        $this->dispatch('stats-updated');
    }

    public function render()
    {
        return view('livewire.manager.dashboard')
            ->layout('layouts.app.master', ['title' => 'Manager Dashboard - Ghana Legal News']);
    }
}
