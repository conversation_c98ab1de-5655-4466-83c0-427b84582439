<?php

namespace App\Livewire\Editor;

use App\Models\Article;
use App\Models\Comment;
use App\Models\LegalCategory;
use Livewire\Component;

class Dashboard extends Component
{
    public $stats = [];
    public $myArticles;
    public $assignedArticles;
    public $pendingComments;
    public $recentActivity;

    public function mount()
    {
        $this->loadDashboardData();
    }

    public function loadDashboardData()
    {
        $userId = auth()->id();

        // Editor-specific statistics
        $this->stats = [
            'my_articles' => Article::where('author_id', $userId)->count(),
            'published_articles' => Article::where('author_id', $userId)->where('status', 'published')->count(),
            'draft_articles' => Article::where('author_id', $userId)->where('status', 'draft')->count(),
            'articles_this_month' => Article::where('author_id', $userId)->whereMonth('created_at', now()->month)->count(),
            'total_views' => Article::where('author_id', $userId)->sum('views_count'),
            'pending_comments' => Comment::whereHas('article', function($query) use ($userId) {
                $query->where('author_id', $userId);
            })->where('status', 'pending')->count(),
            'categories_used' => Article::where('author_id', $userId)->distinct('category_id')->count(),
            'featured_articles' => Article::where('author_id', $userId)->where('is_featured', true)->count(),
        ];

        // My recent articles
        $this->myArticles = Article::with(['category'])
            ->where('author_id', $userId)
            ->latest()
            ->limit(5)
            ->get();

        // Articles assigned for editing (if any)
        $this->assignedArticles = Article::with(['author', 'category'])
            ->where('status', 'draft')
            ->where('author_id', '!=', $userId)
            ->latest()
            ->limit(3)
            ->get();

        // Comments on my articles needing moderation
        $this->pendingComments = Comment::with(['article', 'user'])
            ->whereHas('article', function($query) use ($userId) {
                $query->where('author_id', $userId);
            })
            ->where('status', 'pending')
            ->latest()
            ->limit(5)
            ->get();

        // Recent activity on my articles
        $this->recentActivity = Comment::with(['article', 'user'])
            ->whereHas('article', function($query) use ($userId) {
                $query->where('author_id', $userId);
            })
            ->where('status', 'approved')
            ->latest()
            ->limit(5)
            ->get();
    }

    public function approveComment($commentId)
    {
        $comment = Comment::find($commentId);
        if ($comment && $comment->article->author_id === auth()->id()) {
            $comment->approve(auth()->id());
            $this->loadDashboardData();
            $this->dispatch('comment-approved', ['message' => 'Comment approved successfully']);
        }
    }

    public function rejectComment($commentId)
    {
        $comment = Comment::find($commentId);
        if ($comment && $comment->article->author_id === auth()->id()) {
            $comment->reject('Rejected by editor');
            $this->loadDashboardData();
            $this->dispatch('comment-rejected', ['message' => 'Comment rejected']);
        }
    }

    public function refreshStats()
    {
        $this->loadDashboardData();
        $this->dispatch('stats-updated');
    }

    public function render()
    {
        return view('livewire.editor.dashboard')
            ->layout('layouts.app.master', ['title' => 'Editor Dashboard - Ghana Legal News']);
    }
}
