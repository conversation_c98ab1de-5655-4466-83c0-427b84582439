<?php

namespace App\Livewire\Author;

use App\Models\Article;
use App\Models\Comment;
use App\Models\LegalCategory;
use Livewire\Component;

class Dashboard extends Component
{
    public $stats = [];
    public $myArticles;
    public $recentComments;
    public $popularArticles;
    public $writingGoals;

    public function mount()
    {
        $this->loadDashboardData();
    }

    public function loadDashboardData()
    {
        $userId = auth()->id();

        // Author-specific statistics
        $this->stats = [
            'total_articles' => Article::where('author_id', $userId)->count(),
            'published_articles' => Article::where('author_id', $userId)->where('status', 'published')->count(),
            'draft_articles' => Article::where('author_id', $userId)->where('status', 'draft')->count(),
            'total_views' => Article::where('author_id', $userId)->sum('views_count'),
            'total_comments' => Comment::whereHas('article', function($query) use ($userId) {
                $query->where('author_id', $userId);
            })->where('status', 'approved')->count(),
            'articles_this_month' => Article::where('author_id', $userId)->whereMonth('created_at', now()->month)->count(),
            'featured_articles' => Article::where('author_id', $userId)->where('is_featured', true)->count(),
            'breaking_news' => Article::where('author_id', $userId)->where('is_breaking', true)->count(),
        ];

        // My recent articles
        $this->myArticles = Article::with(['category'])
            ->where('author_id', $userId)
            ->latest()
            ->limit(5)
            ->get();

        // Recent comments on my articles
        $this->recentComments = Comment::with(['article', 'user'])
            ->whereHas('article', function($query) use ($userId) {
                $query->where('author_id', $userId);
            })
            ->where('status', 'approved')
            ->latest()
            ->limit(5)
            ->get();

        // My most popular articles
        $this->popularArticles = Article::where('author_id', $userId)
            ->where('status', 'published')
            ->orderBy('views_count', 'desc')
            ->limit(5)
            ->get();

        // Writing goals (articles per month)
        $this->writingGoals = [
            'monthly_goal' => 10, // Can be made configurable
            'current_month' => $this->stats['articles_this_month'],
            'progress_percentage' => min(100, ($this->stats['articles_this_month'] / 10) * 100),
        ];
    }

    public function createArticle()
    {
        return redirect()->route('author.articles.create');
    }

    public function viewArticle($articleId)
    {
        return redirect()->route('author.articles.edit', $articleId);
    }

    public function refreshStats()
    {
        $this->loadDashboardData();
        $this->dispatch('stats-updated');
    }

    public function render()
    {
        return view('livewire.author.dashboard')
            ->layout('layouts.app.master', ['title' => 'Author Dashboard - Ghana Legal News']);
    }
}
