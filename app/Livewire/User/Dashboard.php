<?php

namespace App\Livewire\User;

use App\Models\Article;
use App\Models\Comment;
use App\Models\LegalCategory;
use Livewire\Component;

class Dashboard extends Component
{
    public $stats = [];
    public $recentArticles;
    public $myComments;
    public $bookmarkedArticles;
    public $recommendedArticles;
    public $popularCategories;

    public function mount()
    {
        $this->loadDashboardData();
    }

    public function loadDashboardData()
    {
        $userId = auth()->id();

        // User-specific statistics
        $this->stats = [
            'comments_made' => Comment::where('user_id', $userId)->count(),
            'approved_comments' => Comment::where('user_id', $userId)->where('status', 'approved')->count(),
            'articles_read' => 0, // Can be tracked with a separate table
            'favorite_categories' => 3, // Can be calculated based on reading history
            'member_since' => auth()->user()->created_at->format('M Y'),
            'reading_streak' => 0, // Can be calculated based on daily visits
        ];

        // Recent published articles
        $this->recentArticles = Article::with(['author', 'category'])
            ->where('status', 'published')
            ->latest('published_at')
            ->limit(6)
            ->get();

        // My recent comments
        $this->myComments = Comment::with(['article'])
            ->where('user_id', $userId)
            ->latest()
            ->limit(5)
            ->get();

        // Bookmarked articles (placeholder - would need bookmarks table)
        $this->bookmarkedArticles = collect(); // Empty for now

        // Recommended articles based on user activity
        $this->recommendedArticles = Article::with(['author', 'category'])
            ->where('status', 'published')
            ->where('is_featured', true)
            ->latest('published_at')
            ->limit(4)
            ->get();

        // Popular categories
        $this->popularCategories = LegalCategory::withCount('publishedArticles')
            ->where('is_active', true)
            ->orderBy('published_articles_count', 'desc')
            ->limit(6)
            ->get();
    }

    public function viewArticle($articleId)
    {
        $article = Article::find($articleId);
        if ($article && $article->status === 'published') {
            return redirect()->route('articles.show', $article->slug);
        }
    }

    public function viewCategory($categorySlug)
    {
        return redirect()->route('categories.show', $categorySlug);
    }

    public function refreshStats()
    {
        $this->loadDashboardData();
        $this->dispatch('stats-updated');
    }

    public function render()
    {
        return view('livewire.user.dashboard')
            ->layout('layouts.app.master', ['title' => 'My Dashboard - Ghana Legal News']);
    }
}
