<?php

namespace App\Livewire\Admin;

use App\Models\Article;
use App\Models\LegalCategory;
use App\Models\LegalCase;
use App\Models\LawFirm;
use App\Models\Lawyer;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class Dashboard extends Component
{
    public $stats = [];
    public $recentArticles;
    public $popularCategories;
    public $articlesByMonth;
    public $articlesByStatus;
    public $articlesByType;
    public $recentCases;
    public $topArticles;
    public $breakingNewsCount;
    public $featuredArticlesCount;

    public function mount()
    {
        $this->loadDashboardData();
    }

    public function loadDashboardData()
    {
        // Get key statistics
        $this->stats = [
            'total_articles' => Article::count(),
            'published_articles' => Article::where('status', 'published')->count(),
            'draft_articles' => Article::where('status', 'draft')->count(),
            'total_categories' => LegalCategory::count(),
            'active_categories' => LegalCategory::where('is_active', true)->count(),
            'total_law_firms' => LawFirm::count(),
            'verified_law_firms' => LawFirm::where('is_verified', true)->count(),
            'total_lawyers' => Lawyer::count(),
            'total_cases' => LegalCase::count(),
            'landmark_cases' => LegalCase::where('is_landmark', true)->count(),
            'total_users' => User::count(),
            'authors' => User::whereIn('role', ['admin', 'manager', 'editor', 'author'])->count(),
        ];

        // Recent articles
        $this->recentArticles = Article::with(['author', 'category'])
            ->latest()
            ->limit(5)
            ->get();

        // Popular categories (by article count)
        $this->popularCategories = LegalCategory::withCount('publishedArticles')
            ->orderBy('published_articles_count', 'desc')
            ->limit(5)
            ->get();

        // Article statistics by month (last 6 months)
        $this->articlesByMonth = Article::select(
                DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'),
                DB::raw('COUNT(*) as count')
            )
            ->where('created_at', '>=', now()->subMonths(6))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Articles by status
        $this->articlesByStatus = Article::select('status', DB::raw('COUNT(*) as count'))
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status');

        // Articles by type
        $this->articlesByType = Article::select('type', DB::raw('COUNT(*) as count'))
            ->groupBy('type')
            ->get()
            ->pluck('count', 'type');

        // Recent legal cases
        $this->recentCases = LegalCase::latest()
            ->limit(5)
            ->get();

        // Top performing articles (by views)
        $this->topArticles = Article::where('status', 'published')
            ->orderBy('views_count', 'desc')
            ->limit(5)
            ->get();

        // Breaking news count
        $this->breakingNewsCount = Article::where('is_breaking', true)
            ->where('status', 'published')
            ->count();

        // Featured articles count
        $this->featuredArticlesCount = Article::where('is_featured', true)
            ->where('status', 'published')
            ->count();
    }

    public function refreshStats()
    {
        $this->loadDashboardData();
        $this->dispatch('stats-updated');
    }

    public function render()
    {
        return view('livewire.admin.dashboard')
            ->layout('layouts.app.master', ['title' => 'Admin Dashboard - Ghana Legal News']);
    }
}
