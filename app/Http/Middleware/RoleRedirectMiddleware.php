<?php

namespace App\Http\Middleware;

use App\Enums\UserRole;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleRedirectMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        $currentRoute = $request->route()->getName();

        // Define role-specific dashboard routes
        $dashboardRoutes = [
            UserRole::ADMIN->value => 'admin.dashboard',
            UserRole::MANAGER->value => 'manager.dashboard',
            UserRole::EDITOR->value => 'editor.dashboard',
            UserRole::AUTHOR->value => 'author.dashboard',
            UserRole::USER->value => 'user.dashboard',
        ];

        // If user is accessing the generic dashboard, redirect to role-specific dashboard
        if ($currentRoute === 'dashboard') {
            $roleDashboard = $dashboardRoutes[$user->role->value] ?? 'user.dashboard';
            return redirect()->route($roleDashboard);
        }

        // Check if user is trying to access a dashboard they don't have permission for
        foreach ($dashboardRoutes as $role => $route) {
            if ($currentRoute === $route && $user->role->value !== $role) {
                // Redirect to their appropriate dashboard
                $userDashboard = $dashboardRoutes[$user->role->value] ?? 'user.dashboard';
                return redirect()->route($userDashboard);
            }
        }

        return $next($request);
    }
}
