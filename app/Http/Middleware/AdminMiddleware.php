<?php

namespace App\Http\Middleware;

use App\Enums\UserRole;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $allowedRoles = [UserRole::ADMIN, UserRole::MANAGER, UserRole::EDITOR];

        if (!in_array(auth()->user()->role, $allowedRoles)) {
            return redirect()->route('dashboard');
        }
        return $next($request);
    }
}
