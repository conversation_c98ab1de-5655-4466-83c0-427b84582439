<?php

namespace App\Http\Controllers;

use App\Models\LegalCategory;
use App\Models\Article;
use App\Models\Advertisement;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    /**
     * Display a listing of categories.
     */
    public function index()
    {
        // Get main categories (level 0) with their children
        $mainCategories = LegalCategory::with(['children' => function ($query) {
                $query->active()->ordered();
            }])
            ->active()
            ->where('level', 0)
            ->withCount('publishedArticles')
            ->ordered()
            ->get();

        // Get featured categories with recent articles
        $featuredCategories = LegalCategory::with(['publishedArticles' => function ($query) {
                $query->with(['author'])
                    ->latest('published_at')
                    ->limit(3);
            }])
            ->active()
            ->featured()
            ->withCount('publishedArticles')
            ->ordered()
            ->get();

        // Get sidebar ads
        $sidebarAds = Advertisement::active()
            ->byPosition('sidebar')
            ->byPriority()
            ->limit(2)
            ->get();

        // Record ad impressions
        foreach ($sidebarAds as $ad) {
            $ad->recordImpression(auth()->id(), request()->url());
        }

        return view('frontend.categories.index', compact(
            'mainCategories',
            'featuredCategories',
            'sidebarAds'
        ));
    }

    /**
     * Display the specified category.
     */
    public function show(LegalCategory $category, Request $request)
    {
        // Check if category is active
        if (!$category->is_active) {
            abort(404);
        }

        $sort = $request->get('sort', 'latest');
        $type = $request->get('type');

        // Get articles in this category and its subcategories
        $categoryIds = $this->getCategoryAndDescendantIds($category);

        $articlesQuery = Article::with(['author', 'category'])
            ->published()
            ->whereIn('category_id', $categoryIds);

        // Filter by type
        if ($type) {
            $articlesQuery->where('type', $type);
        }

        // Sort articles
        switch ($sort) {
            case 'popular':
                $articlesQuery->orderBy('views_count', 'desc');
                break;
            case 'oldest':
                $articlesQuery->oldest('published_at');
                break;
            case 'latest':
            default:
                $articlesQuery->latest('published_at');
                break;
        }

        $articles = $articlesQuery->paginate(12)->appends($request->query());

        // Get featured article for this category
        $featuredArticle = Article::with(['author'])
            ->published()
            ->whereIn('category_id', $categoryIds)
            ->where('is_featured', true)
            ->latest('published_at')
            ->first();

        // Get subcategories
        $subcategories = $category->children()
            ->withCount('publishedArticles')
            ->get();

        // Get parent categories for breadcrumb
        $breadcrumbs = $this->getBreadcrumbs($category);

        // Get related categories (siblings)
        $relatedCategories = collect();
        if ($category->parent_id) {
            $relatedCategories = LegalCategory::where('parent_id', $category->parent_id)
                ->where('id', '!=', $category->id)
                ->active()
                ->withCount('publishedArticles')
                ->ordered()
                ->limit(5)
                ->get();
        }

        // Get sidebar ads
        $sidebarAds = Advertisement::active()
            ->byPosition('sidebar')
            ->byPriority()
            ->limit(3)
            ->get();

        // Get content ads
        $contentAds = Advertisement::active()
            ->byPosition('content_middle')
            ->byPriority()
            ->limit(1)
            ->get();

        // Record ad impressions
        foreach ([$sidebarAds, $contentAds] as $adGroup) {
            foreach ($adGroup as $ad) {
                $ad->recordImpression(auth()->id(), request()->url());
            }
        }

        return view('frontend.categories.show', compact(
            'category',
            'articles',
            'featuredArticle',
            'subcategories',
            'breadcrumbs',
            'relatedCategories',
            'sort',
            'type',
            'sidebarAds',
            'contentAds'
        ));
    }

    /**
     * Get category and all its descendant IDs.
     */
    private function getCategoryAndDescendantIds(LegalCategory $category): array
    {
        $ids = [$category->id];
        
        $descendants = LegalCategory::where('path', 'like', $category->path . '/%')
            ->active()
            ->pluck('id')
            ->toArray();
            
        return array_merge($ids, $descendants);
    }

    /**
     * Get breadcrumb trail for category.
     */
    private function getBreadcrumbs(LegalCategory $category): array
    {
        $breadcrumbs = [];
        $ancestors = $category->ancestors();
        
        foreach ($ancestors as $ancestor) {
            $breadcrumbs[] = [
                'name' => $ancestor->name,
                'url' => route('categories.show', $ancestor->slug),
            ];
        }
        
        $breadcrumbs[] = [
            'name' => $category->name,
            'url' => null, // Current page
        ];
        
        return $breadcrumbs;
    }

    /**
     * Get category hierarchy as JSON for API.
     */
    public function hierarchy()
    {
        $categories = LegalCategory::with(['children' => function ($query) {
                $query->active()->ordered()->with(['children' => function ($q) {
                    $q->active()->ordered();
                }]);
            }])
            ->active()
            ->where('level', 0)
            ->ordered()
            ->get();

        return response()->json($categories);
    }

    /**
     * Search within a specific category.
     */
    public function search(LegalCategory $category, Request $request)
    {
        $query = $request->get('q');
        
        if (empty($query)) {
            return redirect()->route('categories.show', $category->slug);
        }

        // Get category and descendant IDs
        $categoryIds = $this->getCategoryAndDescendantIds($category);

        $articles = Article::with(['author', 'category'])
            ->published()
            ->whereIn('category_id', $categoryIds)
            ->where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('excerpt', 'like', "%{$query}%")
                  ->orWhere('content', 'like', "%{$query}%");
            })
            ->latest('published_at')
            ->paginate(12)
            ->appends($request->query());

        // Get breadcrumbs
        $breadcrumbs = $this->getBreadcrumbs($category);

        // Get sidebar ads
        $sidebarAds = Advertisement::active()
            ->byPosition('sidebar')
            ->byPriority()
            ->limit(2)
            ->get();

        // Record ad impressions
        foreach ($sidebarAds as $ad) {
            $ad->recordImpression(auth()->id(), request()->url());
        }

        return view('frontend.categories.search', compact(
            'category',
            'articles',
            'query',
            'breadcrumbs',
            'sidebarAds'
        ));
    }
}
