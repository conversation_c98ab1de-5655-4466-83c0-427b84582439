<?php

namespace App\Http\Controllers;

use App\Models\Article;
use App\Models\LegalCategory;
use App\Models\Advertisement;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Display the homepage.
     */
    public function index()
    {
        // Featured articles
        $featuredArticles = Article::with(['author', 'category'])
            ->published()
            ->featured()
            ->latest('published_at')
            ->limit(6)
            ->get();

        // Breaking news
        $breakingNews = Article::with(['author', 'category'])
            ->published()
            ->breaking()
            ->latest('published_at')
            ->limit(3)
            ->get();

        // Latest articles by category
        $categorizedArticles = LegalCategory::with(['publishedArticles' => function ($query) {
                $query->with(['author'])
                    ->latest('published_at')
                    ->limit(4);
            }])
            ->active()
            ->featured()
            ->ordered()
            ->limit(6)
            ->get();

        // Most read articles
        $mostReadArticles = Article::with(['author', 'category'])
            ->published()
            ->orderBy('views_count', 'desc')
            ->limit(5)
            ->get();

        // Recent articles
        $recentArticles = Article::with(['author', 'category'])
            ->published()
            ->latest('published_at')
            ->limit(8)
            ->get();

        // Get advertisements for homepage
        $headerAds = Advertisement::active()
            ->byPosition('header')
            ->byPriority()
            ->limit(1)
            ->get();

        $sidebarAds = Advertisement::active()
            ->byPosition('sidebar')
            ->byPriority()
            ->limit(3)
            ->get();

        $contentAds = Advertisement::active()
            ->byPosition('content_middle')
            ->byPriority()
            ->limit(2)
            ->get();

        // Record ad impressions
        foreach ($headerAds as $ad) {
            $ad->recordImpression(auth()->id(), request()->url());
        }
        foreach ($sidebarAds as $ad) {
            $ad->recordImpression(auth()->id(), request()->url());
        }
        foreach ($contentAds as $ad) {
            $ad->recordImpression(auth()->id(), request()->url());
        }

        return view('frontend.home', compact(
            'featuredArticles',
            'breakingNews',
            'categorizedArticles',
            'mostReadArticles',
            'recentArticles',
            'headerAds',
            'sidebarAds',
            'contentAds'
        ));
    }

    /**
     * Display search results.
     */
    public function search(Request $request)
    {
        $query = $request->get('q');
        $category = $request->get('category');
        $type = $request->get('type');

        if (empty($query)) {
            return redirect()->route('home');
        }

        $articlesQuery = Article::with(['author', 'category'])
            ->published()
            ->where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('excerpt', 'like', "%{$query}%")
                  ->orWhere('content', 'like', "%{$query}%");
            });

        // Filter by category
        if ($category) {
            $articlesQuery->where('category_id', $category);
        }

        // Filter by type
        if ($type) {
            $articlesQuery->where('type', $type);
        }

        $articles = $articlesQuery->latest('published_at')
            ->paginate(12)
            ->appends($request->query());

        // Get categories for filter
        $categories = LegalCategory::active()->ordered()->get();

        // Get sidebar ads
        $sidebarAds = Advertisement::active()
            ->byPosition('sidebar')
            ->byPriority()
            ->limit(2)
            ->get();

        // Record ad impressions
        foreach ($sidebarAds as $ad) {
            $ad->recordImpression(auth()->id(), request()->url());
        }

        return view('frontend.search', compact(
            'articles',
            'query',
            'category',
            'type',
            'categories',
            'sidebarAds'
        ));
    }

    /**
     * Display about page.
     */
    public function about()
    {
        return view('frontend.about');
    }

    /**
     * Display contact page.
     */
    public function contact()
    {
        return view('frontend.contact');
    }

    /**
     * Handle contact form submission.
     */
    public function contactSubmit(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
        ]);

        // Here you would typically send an email or store the message
        // For now, we'll just redirect with a success message

        return redirect()->route('contact')
            ->with('success', 'Thank you for your message. We will get back to you soon!');
    }

    /**
     * Handle ad clicks.
     */
    public function adClick(Request $request, $adId)
    {
        $ad = Advertisement::find($adId);
        
        if ($ad && $ad->shouldDisplay()) {
            $ad->recordClick(auth()->id(), $request->get('page_url'), $request->get('referrer'));
            
            return redirect($ad->click_url);
        }

        return redirect()->route('home');
    }
}
