<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\LegalCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class LegalCategoryController extends Controller
{
    /**
     * Display a listing of legal categories.
     */
    public function index(Request $request)
    {
        $query = LegalCategory::withCount('articles');

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $categories = $query->ordered()->paginate(15);
        
        return view('admin.categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new category.
     */
    public function create()
    {
        return view('admin.categories.create');
    }

    /**
     * Store a newly created category.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:legal_categories,slug',
            'description' => 'nullable|string|max:1000',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'icon' => 'nullable|string|max:50',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
        ]);

        // Handle slug
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        // Set default sort order
        if (empty($validated['sort_order'])) {
            $validated['sort_order'] = LegalCategory::max('sort_order') + 1;
        }

        LegalCategory::create($validated);

        return redirect()->route('admin.categories.index')
            ->with('success', 'Legal category created successfully.');
    }

    /**
     * Display the specified category.
     */
    public function show(LegalCategory $category)
    {
        $category->loadCount('articles', 'publishedArticles', 'featuredArticles');
        $recentArticles = $category->publishedArticles()
            ->with('author')
            ->latest('published_at')
            ->limit(10)
            ->get();

        return view('admin.categories.show', compact('category', 'recentArticles'));
    }

    /**
     * Show the form for editing the specified category.
     */
    public function edit(LegalCategory $category)
    {
        return view('admin.categories.edit', compact('category'));
    }

    /**
     * Update the specified category.
     */
    public function update(Request $request, LegalCategory $category)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:legal_categories,slug,' . $category->id,
            'description' => 'nullable|string|max:1000',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'icon' => 'nullable|string|max:50',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
        ]);

        // Handle slug
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        $category->update($validated);

        return redirect()->route('admin.categories.index')
            ->with('success', 'Legal category updated successfully.');
    }

    /**
     * Remove the specified category.
     */
    public function destroy(LegalCategory $category)
    {
        // Check if category has articles
        if ($category->articles()->count() > 0) {
            return redirect()->route('admin.categories.index')
                ->with('error', 'Cannot delete category that has articles. Please move or delete the articles first.');
        }

        $category->delete();

        return redirect()->route('admin.categories.index')
            ->with('success', 'Legal category deleted successfully.');
    }

    /**
     * Update the sort order of categories.
     */
    public function updateOrder(Request $request)
    {
        $validated = $request->validate([
            'categories' => 'required|array',
            'categories.*.id' => 'required|exists:legal_categories,id',
            'categories.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($validated['categories'] as $categoryData) {
            LegalCategory::where('id', $categoryData['id'])
                ->update(['sort_order' => $categoryData['sort_order']]);
        }

        return response()->json(['success' => true]);
    }

    /**
     * Toggle the active status of a category.
     */
    public function toggleStatus(LegalCategory $category)
    {
        $category->update(['is_active' => !$category->is_active]);

        $status = $category->is_active ? 'activated' : 'deactivated';
        
        return redirect()->route('admin.categories.index')
            ->with('success', "Category {$status} successfully.");
    }

    /**
     * Toggle the featured status of a category.
     */
    public function toggleFeatured(LegalCategory $category)
    {
        $category->update(['is_featured' => !$category->is_featured]);

        $status = $category->is_featured ? 'featured' : 'unfeatured';
        
        return redirect()->route('admin.categories.index')
            ->with('success', "Category {$status} successfully.");
    }
}
