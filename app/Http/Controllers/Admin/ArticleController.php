<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\LegalCategory;
use App\Models\Tag;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ArticleController extends Controller
{
    /**
     * Display a listing of articles.
     */
    public function index(Request $request)
    {
        $query = Article::with(['author', 'category', 'tags'])
            ->orderBy('created_at', 'desc');

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category_id', $request->category);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        $articles = $query->paginate(15);
        $categories = LegalCategory::active()->ordered()->get();
        
        return view('admin.articles.index', compact('articles', 'categories'));
    }

    /**
     * Show the form for creating a new article.
     */
    public function create()
    {
        $categories = LegalCategory::active()->ordered()->get();
        $authors = User::whereIn('role', ['admin', 'manager', 'editor', 'author'])
            ->orderBy('name')
            ->get();
        $tags = Tag::active()->popular(50)->get();

        return view('admin.articles.create', compact('categories', 'authors', 'tags'));
    }

    /**
     * Store a newly created article.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:articles,slug',
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'status' => 'required|in:draft,published,scheduled,archived',
            'type' => 'required|in:news,analysis,opinion,case_study,interview',
            'is_featured' => 'boolean',
            'is_breaking' => 'boolean',
            'is_premium' => 'boolean',
            'published_at' => 'nullable|date',
            'scheduled_at' => 'nullable|date',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|array',
            'author_id' => 'required|exists:users,id',
            'category_id' => 'required|exists:legal_categories,id',
            'tags' => 'nullable|array',
            'tags.*' => 'string',
        ]);

        // Handle slug
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $validated['featured_image'] = $request->file('featured_image')
                ->store('articles/featured', 'public');
        }

        // Handle gallery images upload
        if ($request->hasFile('gallery_images')) {
            $galleryImages = [];
            foreach ($request->file('gallery_images') as $image) {
                $galleryImages[] = $image->store('articles/gallery', 'public');
            }
            $validated['gallery_images'] = $galleryImages;
        }

        // Set created_by and updated_by
        $validated['created_by'] = Auth::id();
        $validated['updated_by'] = Auth::id();

        // Handle published_at
        if ($validated['status'] === 'published' && empty($validated['published_at'])) {
            $validated['published_at'] = now();
        }

        $article = Article::create($validated);

        // Handle tags
        if (!empty($validated['tags'])) {
            $tagIds = [];
            foreach ($validated['tags'] as $tagName) {
                $tag = Tag::findOrCreateByName(trim($tagName));
                $tagIds[] = $tag->id;
                $tag->incrementUsage();
            }
            $article->tags()->sync($tagIds);
        }

        return redirect()->route('admin.articles.index')
            ->with('success', 'Article created successfully.');
    }

    /**
     * Display the specified article.
     */
    public function show(Article $article)
    {
        $article->load(['author', 'category', 'tags', 'creator', 'updater']);
        return view('admin.articles.show', compact('article'));
    }

    /**
     * Show the form for editing the specified article.
     */
    public function edit(Article $article)
    {
        $categories = LegalCategory::active()->ordered()->get();
        $authors = User::whereIn('role', ['admin', 'manager', 'editor', 'author'])
            ->orderBy('name')
            ->get();
        $tags = Tag::active()->popular(50)->get();
        $article->load(['tags']);

        return view('admin.articles.edit', compact('article', 'categories', 'authors', 'tags'));
    }

    /**
     * Update the specified article.
     */
    public function update(Request $request, Article $article)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:articles,slug,' . $article->id,
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'status' => 'required|in:draft,published,scheduled,archived',
            'type' => 'required|in:news,analysis,opinion,case_study,interview',
            'is_featured' => 'boolean',
            'is_breaking' => 'boolean',
            'is_premium' => 'boolean',
            'published_at' => 'nullable|date',
            'scheduled_at' => 'nullable|date',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|array',
            'author_id' => 'required|exists:users,id',
            'category_id' => 'required|exists:legal_categories,id',
            'tags' => 'nullable|array',
            'tags.*' => 'string',
            'remove_featured_image' => 'boolean',
        ]);

        // Handle slug
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image
            if ($article->featured_image) {
                Storage::disk('public')->delete($article->featured_image);
            }
            $validated['featured_image'] = $request->file('featured_image')
                ->store('articles/featured', 'public');
        } elseif ($request->boolean('remove_featured_image')) {
            if ($article->featured_image) {
                Storage::disk('public')->delete($article->featured_image);
            }
            $validated['featured_image'] = null;
        }

        // Handle gallery images upload
        if ($request->hasFile('gallery_images')) {
            $galleryImages = $article->gallery_images ?? [];
            foreach ($request->file('gallery_images') as $image) {
                $galleryImages[] = $image->store('articles/gallery', 'public');
            }
            $validated['gallery_images'] = $galleryImages;
        }

        // Set updated_by
        $validated['updated_by'] = Auth::id();

        // Handle published_at
        if ($validated['status'] === 'published' && empty($validated['published_at'])) {
            $validated['published_at'] = now();
        }

        $article->update($validated);

        // Handle tags
        if (isset($validated['tags'])) {
            // Decrement usage count for old tags
            foreach ($article->tags as $oldTag) {
                $oldTag->decrementUsage();
            }

            $tagIds = [];
            foreach ($validated['tags'] as $tagName) {
                $tag = Tag::findOrCreateByName(trim($tagName));
                $tagIds[] = $tag->id;
                $tag->incrementUsage();
            }
            $article->tags()->sync($tagIds);
        }

        return redirect()->route('admin.articles.index')
            ->with('success', 'Article updated successfully.');
    }

    /**
     * Remove the specified article.
     */
    public function destroy(Article $article)
    {
        // Delete associated images
        if ($article->featured_image) {
            Storage::disk('public')->delete($article->featured_image);
        }

        if ($article->gallery_images) {
            foreach ($article->gallery_images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        // Decrement tag usage counts
        foreach ($article->tags as $tag) {
            $tag->decrementUsage();
        }

        $article->delete();

        return redirect()->route('admin.articles.index')
            ->with('success', 'Article deleted successfully.');
    }
}
