<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\LegalCategory;
use App\Models\LegalCase;
use App\Models\LawFirm;
use App\Models\Lawyer;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index()
    {
        // Get key statistics
        $stats = [
            'total_articles' => Article::count(),
            'published_articles' => Article::where('status', 'published')->count(),
            'draft_articles' => Article::where('status', 'draft')->count(),
            'total_categories' => LegalCategory::count(),
            'active_categories' => LegalCategory::where('is_active', true)->count(),
            'total_law_firms' => LawFirm::count(),
            'verified_law_firms' => LawFirm::where('is_verified', true)->count(),
            'total_lawyers' => Lawyer::count(),
            'total_cases' => LegalCase::count(),
            'landmark_cases' => LegalCase::where('is_landmark', true)->count(),
            'total_users' => User::count(),
            'authors' => User::whereIn('role', ['admin', 'manager', 'editor', 'author'])->count(),
        ];

        // Recent articles
        $recentArticles = Article::with(['author', 'category'])
            ->latest()
            ->limit(5)
            ->get();

        // Popular categories (by article count)
        $popularCategories = LegalCategory::withCount('publishedArticles')
            ->orderBy('published_articles_count', 'desc')
            ->limit(5)
            ->get();

        // Article statistics by month (last 6 months)
        $articlesByMonth = Article::select(
                DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'),
                DB::raw('COUNT(*) as count')
            )
            ->where('created_at', '>=', now()->subMonths(6))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Articles by status
        $articlesByStatus = Article::select('status', DB::raw('COUNT(*) as count'))
            ->groupBy('status')
            ->get()
            ->pluck('count', 'status');

        // Articles by type
        $articlesByType = Article::select('type', DB::raw('COUNT(*) as count'))
            ->groupBy('type')
            ->get()
            ->pluck('count', 'type');

        // Recent legal cases
        $recentCases = LegalCase::latest()
            ->limit(5)
            ->get();

        // Top performing articles (by views)
        $topArticles = Article::where('status', 'published')
            ->orderBy('views_count', 'desc')
            ->limit(5)
            ->get();

        // Breaking news count
        $breakingNewsCount = Article::where('is_breaking', true)
            ->where('status', 'published')
            ->count();

        // Featured articles count
        $featuredArticlesCount = Article::where('is_featured', true)
            ->where('status', 'published')
            ->count();

        return view('admin.dashboard', compact(
            'stats',
            'recentArticles',
            'popularCategories',
            'articlesByMonth',
            'articlesByStatus',
            'articlesByType',
            'recentCases',
            'topArticles',
            'breakingNewsCount',
            'featuredArticlesCount'
        ));
    }

    /**
     * Get dashboard statistics for AJAX requests.
     */
    public function getStats()
    {
        $stats = [
            'articles' => [
                'total' => Article::count(),
                'published' => Article::where('status', 'published')->count(),
                'draft' => Article::where('status', 'draft')->count(),
                'scheduled' => Article::where('status', 'scheduled')->count(),
                'today' => Article::whereDate('created_at', today())->count(),
                'this_week' => Article::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
                'this_month' => Article::whereMonth('created_at', now()->month)->count(),
            ],
            'categories' => [
                'total' => LegalCategory::count(),
                'active' => LegalCategory::where('is_active', true)->count(),
                'featured' => LegalCategory::where('is_featured', true)->count(),
            ],
            'legal_directory' => [
                'law_firms' => LawFirm::count(),
                'verified_firms' => LawFirm::where('is_verified', true)->count(),
                'lawyers' => Lawyer::count(),
                'verified_lawyers' => Lawyer::where('gba_verified', true)->count(),
            ],
            'cases' => [
                'total' => LegalCase::count(),
                'landmark' => LegalCase::where('is_landmark', true)->count(),
                'pending' => LegalCase::where('case_status', 'pending')->count(),
                'decided' => LegalCase::where('case_status', 'decided')->count(),
            ],
            'users' => [
                'total' => User::count(),
                'admins' => User::where('role', 'admin')->count(),
                'authors' => User::whereIn('role', ['author', 'editor'])->count(),
                'active_today' => User::whereDate('updated_at', today())->count(),
            ]
        ];

        return response()->json($stats);
    }

    /**
     * Get chart data for articles over time.
     */
    public function getArticleChartData(Request $request)
    {
        $period = $request->get('period', '6months');
        
        switch ($period) {
            case '7days':
                $data = Article::select(
                        DB::raw('DATE(created_at) as date'),
                        DB::raw('COUNT(*) as count')
                    )
                    ->where('created_at', '>=', now()->subDays(7))
                    ->groupBy('date')
                    ->orderBy('date')
                    ->get();
                break;
            case '30days':
                $data = Article::select(
                        DB::raw('DATE(created_at) as date'),
                        DB::raw('COUNT(*) as count')
                    )
                    ->where('created_at', '>=', now()->subDays(30))
                    ->groupBy('date')
                    ->orderBy('date')
                    ->get();
                break;
            case '6months':
            default:
                $data = Article::select(
                        DB::raw('DATE_FORMAT(created_at, "%Y-%m") as date'),
                        DB::raw('COUNT(*) as count')
                    )
                    ->where('created_at', '>=', now()->subMonths(6))
                    ->groupBy('date')
                    ->orderBy('date')
                    ->get();
                break;
        }

        return response()->json($data);
    }
}
