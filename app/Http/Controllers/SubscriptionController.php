<?php

namespace App\Http\Controllers;

use App\Models\SubscriptionPlan;
use App\Models\UserSubscription;
use App\Models\PaymentTransaction;
use App\Models\DiscountCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class SubscriptionController extends Controller
{
    /**
     * Display subscription plans.
     */
    public function index()
    {
        $plans = SubscriptionPlan::active()
            ->orderBy('sort_order')
            ->orderBy('price')
            ->get();

        $userSubscription = null;
        if (auth()->check()) {
            $userSubscription = auth()->user()->activeSubscription();
        }

        return view('frontend.subscriptions.index', compact('plans', 'userSubscription'));
    }

    /**
     * Show subscription checkout page.
     */
    public function checkout(SubscriptionPlan $plan, Request $request)
    {
        if (!auth()->check()) {
            return redirect()->route('login')
                ->with('intended_url', route('subscriptions.checkout', $plan));
        }

        $user = auth()->user();
        
        // Check if user already has an active subscription
        $activeSubscription = $user->activeSubscription();
        if ($activeSubscription) {
            return redirect()->route('subscriptions.manage')
                ->with('error', 'You already have an active subscription.');
        }

        $discountCode = null;
        $discountAmount = 0;
        $finalPrice = $plan->price;

        // Check for discount code
        if ($request->has('discount_code')) {
            $discountCode = DiscountCode::where('code', $request->discount_code)
                ->where('is_active', true)
                ->where('starts_at', '<=', now())
                ->where('expires_at', '>=', now())
                ->first();

            if ($discountCode && $this->canUseDiscountCode($discountCode, $user, $plan)) {
                $discountAmount = $this->calculateDiscount($discountCode, $plan->price);
                $finalPrice = max(0, $plan->price - $discountAmount);
            }
        }

        return view('frontend.subscriptions.checkout', compact(
            'plan',
            'discountCode',
            'discountAmount',
            'finalPrice'
        ));
    }

    /**
     * Process subscription payment.
     */
    public function processPayment(Request $request, SubscriptionPlan $plan)
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $validated = $request->validate([
            'payment_method' => 'required|in:stripe,paypal,mobile_money',
            'discount_code' => 'nullable|string',
            'mobile_network' => 'required_if:payment_method,mobile_money|in:MTN,Vodafone,AirtelTigo',
            'mobile_number' => 'required_if:payment_method,mobile_money|string',
        ]);

        $user = auth()->user();
        
        // Check if user already has an active subscription
        if ($user->activeSubscription()) {
            return redirect()->route('subscriptions.manage')
                ->with('error', 'You already have an active subscription.');
        }

        $discountCode = null;
        $discountAmount = 0;
        $finalPrice = $plan->price;

        // Validate discount code
        if (!empty($validated['discount_code'])) {
            $discountCode = DiscountCode::where('code', $validated['discount_code'])
                ->where('is_active', true)
                ->where('starts_at', '<=', now())
                ->where('expires_at', '>=', now())
                ->first();

            if ($discountCode && $this->canUseDiscountCode($discountCode, $user, $plan)) {
                $discountAmount = $this->calculateDiscount($discountCode, $plan->price);
                $finalPrice = max(0, $plan->price - $discountAmount);
            }
        }

        // Create payment transaction
        $transaction = PaymentTransaction::create([
            'user_id' => $user->id,
            'transaction_reference' => 'TXN_' . Str::upper(Str::random(12)),
            'payment_gateway' => $validated['payment_method'],
            'amount' => $finalPrice,
            'currency' => 'GHS',
            'status' => 'pending',
            'type' => 'subscription',
            'description' => "Subscription to {$plan->name} plan",
        ]);

        // Process payment based on method
        switch ($validated['payment_method']) {
            case 'stripe':
                return $this->processStripePayment($transaction, $plan, $discountCode);
            
            case 'paypal':
                return $this->processPayPalPayment($transaction, $plan, $discountCode);
            
            case 'mobile_money':
                return $this->processMobileMoneyPayment(
                    $transaction, 
                    $plan, 
                    $validated['mobile_network'], 
                    $validated['mobile_number'],
                    $discountCode
                );
            
            default:
                return redirect()->back()->with('error', 'Invalid payment method.');
        }
    }

    /**
     * Handle successful payment callback.
     */
    public function paymentSuccess(Request $request, $transactionId)
    {
        $transaction = PaymentTransaction::where('transaction_reference', $transactionId)->first();
        
        if (!$transaction || $transaction->status !== 'pending') {
            return redirect()->route('subscriptions.index')
                ->with('error', 'Invalid transaction.');
        }

        // Mark transaction as completed
        $transaction->update([
            'status' => 'completed',
            'processed_at' => now(),
        ]);

        // Create subscription
        $plan = SubscriptionPlan::find($request->get('plan_id'));
        $this->createSubscription($transaction->user, $plan, $transaction);

        return redirect()->route('subscriptions.manage')
            ->with('success', 'Subscription activated successfully!');
    }

    /**
     * Handle failed payment callback.
     */
    public function paymentFailed(Request $request, $transactionId)
    {
        $transaction = PaymentTransaction::where('transaction_reference', $transactionId)->first();
        
        if ($transaction) {
            $transaction->update(['status' => 'failed']);
        }

        return redirect()->route('subscriptions.index')
            ->with('error', 'Payment failed. Please try again.');
    }

    /**
     * Display user's subscription management page.
     */
    public function manage()
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        $activeSubscription = $user->activeSubscription();
        $subscriptionHistory = $user->subscriptions()->latest()->paginate(10);
        $paymentHistory = $user->paymentTransactions()->latest()->paginate(10);

        return view('frontend.subscriptions.manage', compact(
            'activeSubscription',
            'subscriptionHistory',
            'paymentHistory'
        ));
    }

    /**
     * Cancel user's subscription.
     */
    public function cancel(Request $request)
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();
        $subscription = $user->activeSubscription();

        if (!$subscription) {
            return redirect()->route('subscriptions.manage')
                ->with('error', 'No active subscription found.');
        }

        $subscription->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
        ]);

        return redirect()->route('subscriptions.manage')
            ->with('success', 'Subscription cancelled successfully. You will continue to have access until the end of your billing period.');
    }

    /**
     * Apply discount code.
     */
    public function applyDiscount(Request $request)
    {
        $validated = $request->validate([
            'code' => 'required|string',
            'plan_id' => 'required|exists:subscription_plans,id',
        ]);

        $plan = SubscriptionPlan::find($validated['plan_id']);
        $discountCode = DiscountCode::where('code', $validated['code'])
            ->where('is_active', true)
            ->where('starts_at', '<=', now())
            ->where('expires_at', '>=', now())
            ->first();

        if (!$discountCode) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired discount code.'
            ]);
        }

        if (!$this->canUseDiscountCode($discountCode, auth()->user(), $plan)) {
            return response()->json([
                'success' => false,
                'message' => 'This discount code cannot be used for this plan or you have exceeded the usage limit.'
            ]);
        }

        $discountAmount = $this->calculateDiscount($discountCode, $plan->price);
        $finalPrice = max(0, $plan->price - $discountAmount);

        return response()->json([
            'success' => true,
            'discount_amount' => $discountAmount,
            'final_price' => $finalPrice,
            'discount_code' => $discountCode->only(['code', 'name', 'type', 'value'])
        ]);
    }

    /**
     * Check if user can use discount code.
     */
    private function canUseDiscountCode(DiscountCode $discountCode, $user, SubscriptionPlan $plan): bool
    {
        // Check if code applies to this plan
        if ($discountCode->applicable_plans && !in_array($plan->id, $discountCode->applicable_plans)) {
            return false;
        }

        // Check usage limits
        if ($discountCode->usage_limit && $discountCode->usage_count >= $discountCode->usage_limit) {
            return false;
        }

        // Check user usage limit
        $userUsageCount = \App\Models\DiscountCodeUsage::where('discount_code_id', $discountCode->id)
            ->where('user_id', $user->id)
            ->count();

        if ($userUsageCount >= $discountCode->user_usage_limit) {
            return false;
        }

        return true;
    }

    /**
     * Calculate discount amount.
     */
    private function calculateDiscount(DiscountCode $discountCode, float $price): float
    {
        if ($discountCode->type === 'percentage') {
            return ($price * $discountCode->value) / 100;
        }

        return min($discountCode->value, $price);
    }

    /**
     * Create subscription for user.
     */
    private function createSubscription($user, SubscriptionPlan $plan, PaymentTransaction $transaction): UserSubscription
    {
        $startsAt = now();
        $endsAt = match($plan->billing_cycle) {
            'monthly' => $startsAt->copy()->addMonth(),
            'quarterly' => $startsAt->copy()->addMonths(3),
            'yearly' => $startsAt->copy()->addYear(),
        };

        $trialEndsAt = $plan->trial_days > 0 ? $startsAt->copy()->addDays($plan->trial_days) : null;

        return UserSubscription::create([
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'status' => $plan->trial_days > 0 ? 'trial' : 'active',
            'starts_at' => $startsAt,
            'ends_at' => $endsAt,
            'trial_ends_at' => $trialEndsAt,
            'payment_method' => $transaction->payment_gateway,
            'payment_reference' => $transaction->transaction_reference,
            'amount_paid' => $transaction->amount,
        ]);
    }

    /**
     * Process Stripe payment (placeholder).
     */
    private function processStripePayment($transaction, $plan, $discountCode = null)
    {
        // Implement Stripe payment processing
        // This is a placeholder - you would integrate with Stripe API
        
        return redirect()->route('subscriptions.payment.success', $transaction->transaction_reference)
            ->with('plan_id', $plan->id);
    }

    /**
     * Process PayPal payment (placeholder).
     */
    private function processPayPalPayment($transaction, $plan, $discountCode = null)
    {
        // Implement PayPal payment processing
        // This is a placeholder - you would integrate with PayPal API
        
        return redirect()->route('subscriptions.payment.success', $transaction->transaction_reference)
            ->with('plan_id', $plan->id);
    }

    /**
     * Process Mobile Money payment (placeholder).
     */
    private function processMobileMoneyPayment($transaction, $plan, $network, $phoneNumber, $discountCode = null)
    {
        // Implement Mobile Money payment processing (Flutterwave, Paystack, etc.)
        // This is a placeholder - you would integrate with mobile money API
        
        return redirect()->route('subscriptions.payment.success', $transaction->transaction_reference)
            ->with('plan_id', $plan->id);
    }
}
