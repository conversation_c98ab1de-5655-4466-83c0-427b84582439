<?php

namespace App\Http\Controllers;

use App\Models\Article;
use App\Models\Comment;
use App\Models\Advertisement;
use App\Models\LegalCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ArticleController extends Controller
{
    /**
     * Display a listing of articles.
     */
    public function index(Request $request)
    {
        $category = $request->get('category');
        $type = $request->get('type');
        $sort = $request->get('sort', 'latest');

        $articlesQuery = Article::with(['author', 'category'])
            ->published();

        // Filter by category
        if ($category) {
            $articlesQuery->whereHas('category', function ($q) use ($category) {
                $q->where('slug', $category);
            });
        }

        // Filter by type
        if ($type) {
            $articlesQuery->where('type', $type);
        }

        // Sort articles
        switch ($sort) {
            case 'popular':
                $articlesQuery->orderBy('views_count', 'desc');
                break;
            case 'oldest':
                $articlesQuery->oldest('published_at');
                break;
            case 'latest':
            default:
                $articlesQuery->latest('published_at');
                break;
        }

        $articles = $articlesQuery->paginate(12)->appends($request->query());

        // Get categories for filter
        $categories = LegalCategory::active()->ordered()->get();

        // Get sidebar ads
        $sidebarAds = Advertisement::active()
            ->byPosition('sidebar')
            ->byPriority()
            ->limit(3)
            ->get();

        // Record ad impressions
        foreach ($sidebarAds as $ad) {
            $ad->recordImpression(auth()->id(), request()->url());
        }

        return view('frontend.articles.index', compact(
            'articles',
            'categories',
            'category',
            'type',
            'sort',
            'sidebarAds'
        ));
    }

    /**
     * Display the specified article.
     */
    public function show(Article $article)
    {
        // Check if article is published
        if ($article->status !== 'published') {
            abort(404);
        }

        // Check if premium content and user has access
        if ($article->is_premium && !$this->userHasPremiumAccess($article)) {
            return view('frontend.articles.premium-required', compact('article'));
        }

        // Increment view count
        $article->incrementViews();

        // Load relationships
        $article->load(['author', 'category', 'tags']);

        // Get comments with replies
        $comments = $article->topLevelComments()->paginate(10);

        // Get related articles
        $relatedArticles = Article::with(['author', 'category'])
            ->published()
            ->where('category_id', $article->category_id)
            ->where('id', '!=', $article->id)
            ->latest('published_at')
            ->limit(4)
            ->get();

        // Get advertisements
        $contentTopAds = Advertisement::active()
            ->byPosition('content_top')
            ->byPriority()
            ->limit(1)
            ->get();

        $contentMiddleAds = Advertisement::active()
            ->byPosition('content_middle')
            ->byPriority()
            ->limit(1)
            ->get();

        $contentBottomAds = Advertisement::active()
            ->byPosition('content_bottom')
            ->byPriority()
            ->limit(1)
            ->get();

        $sidebarAds = Advertisement::active()
            ->byPosition('sidebar')
            ->byPriority()
            ->limit(3)
            ->get();

        // Record ad impressions
        foreach ([$contentTopAds, $contentMiddleAds, $contentBottomAds, $sidebarAds] as $adGroup) {
            foreach ($adGroup as $ad) {
                $ad->recordImpression(auth()->id(), request()->url());
            }
        }

        // Track premium content access
        if ($article->is_premium && auth()->check()) {
            $this->trackPremiumAccess($article);
        }

        return view('frontend.articles.show', compact(
            'article',
            'comments',
            'relatedArticles',
            'contentTopAds',
            'contentMiddleAds',
            'contentBottomAds',
            'sidebarAds'
        ));
    }

    /**
     * Store a comment for an article.
     */
    public function storeComment(Request $request, Article $article)
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $validated = $request->validate([
            'content' => 'required|string|max:2000',
            'parent_id' => 'nullable|exists:comments,id',
        ]);

        $comment = Comment::create([
            'article_id' => $article->id,
            'user_id' => auth()->id(),
            'parent_id' => $validated['parent_id'] ?? null,
            'content' => $validated['content'],
            'status' => 'pending', // Requires moderation
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);

        return redirect()->route('articles.show', $article->slug)
            ->with('success', 'Your comment has been submitted and is awaiting moderation.');
    }

    /**
     * Handle comment reactions (like/dislike).
     */
    public function commentReaction(Request $request, Comment $comment)
    {
        if (!auth()->check()) {
            return response()->json(['success' => false, 'message' => 'Authentication required']);
        }

        $validated = $request->validate([
            'type' => 'required|in:like,dislike',
        ]);

        $result = \App\Models\CommentReaction::toggle(
            $comment->id,
            $validated['type'],
            auth()->id()
        );

        // Refresh comment to get updated counts
        $comment->refresh();

        return response()->json([
            'success' => $result['success'],
            'action' => $result['action'],
            'type' => $result['type'],
            'likes_count' => $comment->likes_count,
            'dislikes_count' => $comment->dislikes_count,
        ]);
    }

    /**
     * Report a comment.
     */
    public function reportComment(Request $request, Comment $comment)
    {
        if (!auth()->check()) {
            return response()->json(['success' => false, 'message' => 'Authentication required']);
        }

        $validated = $request->validate([
            'reason' => 'required|in:spam,inappropriate,harassment,off_topic,other',
            'description' => 'nullable|string|max:500',
        ]);

        // Check if user already reported this comment
        $existingReport = \App\Models\CommentReport::where('comment_id', $comment->id)
            ->where('reported_by', auth()->id())
            ->first();

        if ($existingReport) {
            return response()->json(['success' => false, 'message' => 'You have already reported this comment']);
        }

        \App\Models\CommentReport::create([
            'comment_id' => $comment->id,
            'reported_by' => auth()->id(),
            'reason' => $validated['reason'],
            'description' => $validated['description'],
            'ip_address' => request()->ip(),
        ]);

        return response()->json(['success' => true, 'message' => 'Comment reported successfully']);
    }

    /**
     * Check if user has premium access to article.
     */
    private function userHasPremiumAccess(Article $article): bool
    {
        if (!auth()->check()) {
            return false;
        }

        // Check if user has active subscription
        $user = auth()->user();
        $activeSubscription = $user->subscriptions()
            ->where('status', 'active')
            ->where('ends_at', '>', now())
            ->first();

        return (bool) $activeSubscription;
    }

    /**
     * Track premium content access.
     */
    private function trackPremiumAccess(Article $article): void
    {
        \App\Models\PremiumContentAccess::firstOrCreate([
            'user_id' => auth()->id(),
            'article_id' => $article->id,
        ], [
            'accessed_at' => now(),
            'access_type' => 'subscription',
        ]);
    }
}
