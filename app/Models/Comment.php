<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Comment extends Model
{
    use HasFactory, HasUuids;

    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'article_id',
        'user_id',
        'parent_id',
        'content',
        'status',
        'is_featured',
        'likes_count',
        'dislikes_count',
        'replies_count',
        'ip_address',
        'user_agent',
        'approved_at',
        'approved_by',
        'moderation_notes',
    ];

    protected $casts = [
        'is_featured' => 'boolean',
        'likes_count' => 'integer',
        'dislikes_count' => 'integer',
        'replies_count' => 'integer',
        'approved_at' => 'datetime',
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = Str::uuid();
        });

        static::created(function ($model) {
            // Update parent comment replies count
            if ($model->parent_id) {
                Comment::where('id', $model->parent_id)->increment('replies_count');
            }
        });

        static::deleted(function ($model) {
            // Update parent comment replies count
            if ($model->parent_id) {
                Comment::where('id', $model->parent_id)->decrement('replies_count');
            }
        });
    }

    /**
     * Get the article this comment belongs to.
     */
    public function article(): BelongsTo
    {
        return $this->belongsTo(Article::class);
    }

    /**
     * Get the user who wrote this comment.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the parent comment (for replies).
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Comment::class, 'parent_id');
    }

    /**
     * Get the replies to this comment.
     */
    public function replies(): HasMany
    {
        return $this->hasMany(Comment::class, 'parent_id')->where('status', 'approved');
    }

    /**
     * Get all replies (including unapproved).
     */
    public function allReplies(): HasMany
    {
        return $this->hasMany(Comment::class, 'parent_id');
    }

    /**
     * Get the user who approved this comment.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the reactions for this comment.
     */
    public function reactions(): HasMany
    {
        return $this->hasMany(CommentReaction::class);
    }

    /**
     * Get the reports for this comment.
     */
    public function reports(): HasMany
    {
        return $this->hasMany(CommentReport::class);
    }

    /**
     * Scope to get only approved comments.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope to get only pending comments.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get only featured comments.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to get only top-level comments (not replies).
     */
    public function scopeTopLevel($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope to get comments with their replies.
     */
    public function scopeWithReplies($query)
    {
        return $query->with(['replies' => function ($query) {
            $query->approved()->with('user')->orderBy('created_at');
        }]);
    }

    /**
     * Check if the comment is approved.
     */
    public function getIsApprovedAttribute(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if the comment is pending.
     */
    public function getIsPendingAttribute(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the comment is a reply.
     */
    public function getIsReplyAttribute(): bool
    {
        return !is_null($this->parent_id);
    }

    /**
     * Get the comment depth level.
     */
    public function getDepthAttribute(): int
    {
        $depth = 0;
        $parent = $this->parent;
        
        while ($parent) {
            $depth++;
            $parent = $parent->parent;
        }
        
        return $depth;
    }

    /**
     * Approve the comment.
     */
    public function approve($approvedBy = null): void
    {
        $this->update([
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => $approvedBy ?? auth()->id(),
        ]);
    }

    /**
     * Reject the comment.
     */
    public function reject($reason = null): void
    {
        $this->update([
            'status' => 'rejected',
            'moderation_notes' => $reason,
        ]);
    }

    /**
     * Mark as spam.
     */
    public function markAsSpam($reason = null): void
    {
        $this->update([
            'status' => 'spam',
            'moderation_notes' => $reason,
        ]);
    }

    /**
     * Feature the comment.
     */
    public function feature(): void
    {
        $this->update(['is_featured' => true]);
    }

    /**
     * Unfeature the comment.
     */
    public function unfeature(): void
    {
        $this->update(['is_featured' => false]);
    }

    /**
     * Get user's reaction to this comment.
     */
    public function getUserReaction($userId = null): ?CommentReaction
    {
        $userId = $userId ?? auth()->id();
        
        if (!$userId) {
            return null;
        }
        
        return $this->reactions()->where('user_id', $userId)->first();
    }

    /**
     * Check if user has liked this comment.
     */
    public function isLikedBy($userId = null): bool
    {
        $reaction = $this->getUserReaction($userId);
        return $reaction && $reaction->type === 'like';
    }

    /**
     * Check if user has disliked this comment.
     */
    public function isDislikedBy($userId = null): bool
    {
        $reaction = $this->getUserReaction($userId);
        return $reaction && $reaction->type === 'dislike';
    }
}
