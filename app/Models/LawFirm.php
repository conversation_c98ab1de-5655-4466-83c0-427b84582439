<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class LawFirm extends Model
{
    use HasFactory, HasUuids;

    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'name',
        'slug',
        'description',
        'logo',
        'practice_areas',
        'email',
        'phone',
        'website',
        'address',
        'city',
        'region',
        'postal_code',
        'established_year',
        'number_of_lawyers',
        'firm_size',
        'is_verified',
        'is_featured',
        'is_active',
        'gba_registration',
        'gba_verified',
        'meta_title',
        'meta_description',
        'linkedin_url',
        'twitter_url',
        'facebook_url',
    ];

    protected $casts = [
        'practice_areas' => 'array',
        'established_year' => 'integer',
        'number_of_lawyers' => 'integer',
        'is_verified' => 'boolean',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'gba_verified' => 'boolean',
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = Str::uuid();
            if (empty($model->slug)) {
                $model->slug = Str::slug($model->name);
            }
        });

        static::updating(function ($model) {
            if ($model->isDirty('name') && empty($model->slug)) {
                $model->slug = Str::slug($model->name);
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the lawyers associated with this firm.
     */
    public function lawyers(): HasMany
    {
        return $this->hasMany(Lawyer::class);
    }

    /**
     * Get active lawyers associated with this firm.
     */
    public function activeLawyers(): HasMany
    {
        return $this->lawyers()->where('is_active', true);
    }

    /**
     * Scope to get only active firms.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get only verified firms.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope to get only featured firms.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to filter by region.
     */
    public function scopeInRegion($query, $region)
    {
        return $query->where('region', $region);
    }

    /**
     * Scope to filter by city.
     */
    public function scopeInCity($query, $city)
    {
        return $query->where('city', $city);
    }

    /**
     * Scope to filter by firm size.
     */
    public function scopeOfSize($query, $size)
    {
        return $query->where('firm_size', $size);
    }

    /**
     * Get the practice areas as a collection of categories.
     */
    public function getPracticeAreaCategoriesAttribute()
    {
        if (!$this->practice_areas) {
            return collect();
        }

        return LegalCategory::whereIn('id', $this->practice_areas)->get();
    }

    /**
     * Get the full address.
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->region,
            $this->postal_code,
        ]);

        return implode(', ', $parts);
    }

    /**
     * Get the years in operation.
     */
    public function getYearsInOperationAttribute(): ?int
    {
        if (!$this->established_year) {
            return null;
        }

        return now()->year - $this->established_year;
    }

    /**
     * Get Ghana regions for validation.
     */
    public static function getGhanaRegions(): array
    {
        return [
            'Greater Accra',
            'Ashanti',
            'Western',
            'Central',
            'Eastern',
            'Volta',
            'Northern',
            'Upper East',
            'Upper West',
            'Brong-Ahafo',
            'Western North',
            'Ahafo',
            'Bono',
            'Bono East',
            'Oti',
            'North East',
            'Savannah',
        ];
    }
}
