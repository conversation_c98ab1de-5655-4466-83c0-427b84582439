<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class UserSubscription extends Model
{
    use HasFactory, HasUuids;

    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'user_id',
        'plan_id',
        'status',
        'starts_at',
        'ends_at',
        'trial_ends_at',
        'cancelled_at',
        'payment_method',
        'payment_reference',
        'amount_paid',
        'subscription_data',
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'trial_ends_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'amount_paid' => 'decimal:2',
        'subscription_data' => 'array',
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }

    /**
     * Get the user that owns the subscription.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subscription plan.
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class, 'plan_id');
    }

    /**
     * Get payment transactions for this subscription.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(PaymentTransaction::class, 'subscription_id');
    }

    /**
     * Scope to get active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('ends_at', '>', now());
    }

    /**
     * Scope to get trial subscriptions.
     */
    public function scopeTrial($query)
    {
        return $query->where('status', 'trial')
                    ->where('trial_ends_at', '>', now());
    }

    /**
     * Scope to get expired subscriptions.
     */
    public function scopeExpired($query)
    {
        return $query->where('ends_at', '<=', now())
                    ->whereIn('status', ['active', 'trial']);
    }

    /**
     * Scope to get cancelled subscriptions.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', 'cancelled');
    }

    /**
     * Check if subscription is currently active.
     */
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'active' && $this->ends_at > now();
    }

    /**
     * Check if subscription is in trial period.
     */
    public function getIsTrialAttribute(): bool
    {
        return $this->status === 'trial' && 
               $this->trial_ends_at && 
               $this->trial_ends_at > now();
    }

    /**
     * Check if subscription is expired.
     */
    public function getIsExpiredAttribute(): bool
    {
        return $this->ends_at <= now();
    }

    /**
     * Check if subscription is cancelled.
     */
    public function getIsCancelledAttribute(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Get days remaining in subscription.
     */
    public function getDaysRemainingAttribute(): int
    {
        if ($this->is_expired) {
            return 0;
        }

        return max(0, now()->diffInDays($this->ends_at, false));
    }

    /**
     * Get days remaining in trial.
     */
    public function getTrialDaysRemainingAttribute(): int
    {
        if (!$this->is_trial || !$this->trial_ends_at) {
            return 0;
        }

        return max(0, now()->diffInDays($this->trial_ends_at, false));
    }

    /**
     * Get subscription status label.
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'active' => 'Active',
            'trial' => 'Trial',
            'cancelled' => 'Cancelled',
            'expired' => 'Expired',
            'suspended' => 'Suspended',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get subscription status color.
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'active' => 'success',
            'trial' => 'info',
            'cancelled' => 'warning',
            'expired' => 'danger',
            'suspended' => 'secondary',
            default => 'secondary',
        };
    }

    /**
     * Get next billing date.
     */
    public function getNextBillingDateAttribute(): ?\Carbon\Carbon
    {
        if ($this->is_cancelled || $this->is_expired) {
            return null;
        }

        return $this->ends_at;
    }

    /**
     * Cancel the subscription.
     */
    public function cancel(): void
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
        ]);
    }

    /**
     * Renew the subscription.
     */
    public function renew(): void
    {
        $plan = $this->plan;
        
        $newEndsAt = match($plan->billing_cycle) {
            'monthly' => $this->ends_at->addMonth(),
            'quarterly' => $this->ends_at->addMonths(3),
            'yearly' => $this->ends_at->addYear(),
        };

        $this->update([
            'status' => 'active',
            'ends_at' => $newEndsAt,
            'cancelled_at' => null,
        ]);
    }

    /**
     * Suspend the subscription.
     */
    public function suspend(): void
    {
        $this->update(['status' => 'suspended']);
    }

    /**
     * Reactivate the subscription.
     */
    public function reactivate(): void
    {
        if ($this->ends_at > now()) {
            $this->update(['status' => 'active']);
        }
    }

    /**
     * Convert trial to active subscription.
     */
    public function convertTrialToActive(): void
    {
        if ($this->status === 'trial') {
            $this->update(['status' => 'active']);
        }
    }

    /**
     * Check if subscription gives access to premium content.
     */
    public function givesPremiumAccess(): bool
    {
        return $this->is_active || $this->is_trial;
    }

    /**
     * Get subscription duration in human readable format.
     */
    public function getDurationAttribute(): string
    {
        $start = $this->starts_at;
        $end = $this->ends_at;
        
        $days = $start->diffInDays($end);
        
        if ($days < 7) {
            return "{$days} days";
        } elseif ($days < 30) {
            $weeks = round($days / 7);
            return "{$weeks} weeks";
        } elseif ($days < 365) {
            $months = round($days / 30);
            return "{$months} months";
        } else {
            $years = round($days / 365);
            return "{$years} years";
        }
    }
}
