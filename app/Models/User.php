<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Enums\UserRole;
use Illuminate\Support\Str;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;

class User extends Authenticatable
{
    public $incrementing = false; // Disable auto-incrementing
    protected $keyType = 'string'; // Set key type to string


    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'role'=> UserRole::class,
        ];
    }

    /**
     * Get the user's initials
     */
    public function initials(): string
    {
        return Str::of($this->name)
            ->explode(' ')
            ->take(2)
            ->map(fn ($word) => Str::substr($word, 0, 1))
            ->implode('');
    }

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }

    public function getRouteKeyName(): string
    {
        return 'id';
    }

    /**
     * Get articles authored by this user.
     */
    public function authoredArticles(): HasMany
    {
        return $this->hasMany(Article::class, 'author_id');
    }

    /**
     * Get user subscriptions.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Get active subscription.
     */
    public function activeSubscription(): ?UserSubscription
    {
        return $this->subscriptions()
            ->where('status', 'active')
            ->where('ends_at', '>', now())
            ->first();
    }

    /**
     * Get trial subscription.
     */
    public function trialSubscription(): ?UserSubscription
    {
        return $this->subscriptions()
            ->where('status', 'trial')
            ->where('trial_ends_at', '>', now())
            ->first();
    }

    /**
     * Get payment transactions.
     */
    public function paymentTransactions(): HasMany
    {
        return $this->hasMany(PaymentTransaction::class);
    }

    /**
     * Get comments made by user.
     */
    public function comments(): HasMany
    {
        return $this->hasMany(Comment::class);
    }

    /**
     * Check if user has premium access.
     */
    public function hasPremiumAccess(): bool
    {
        $activeSubscription = $this->activeSubscription();
        $trialSubscription = $this->trialSubscription();

        return $activeSubscription || $trialSubscription;
    }

    /**
     * Check if user can access premium content.
     */
    public function canAccessPremiumContent(): bool
    {
        return $this->hasPremiumAccess();
    }

    /**
     * Get articles created by this user.
     */
    public function createdArticles(): HasMany
    {
        return $this->hasMany(Article::class, 'created_by');
    }

    /**
     * Get articles last updated by this user.
     */
    public function updatedArticles(): HasMany
    {
        return $this->hasMany(Article::class, 'updated_by');
    }

    /**
     * Check if user can author articles.
     */
    public function canAuthorArticles(): bool
    {
        return in_array($this->role, [UserRole::ADMIN, UserRole::MANAGER, UserRole::EDITOR, UserRole::AUTHOR]);
    }

    /**
     * Check if user can manage content.
     */
    public function canManageContent(): bool
    {
        return in_array($this->role, [UserRole::ADMIN, UserRole::MANAGER, UserRole::EDITOR]);
    }

    /**
     * Check if user is admin.
     */
    public function isAdmin(): bool
    {
        return $this->role === UserRole::ADMIN;
    }
}
