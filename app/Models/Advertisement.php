<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Advertisement extends Model
{
    use HasFactory, HasUuids;

    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'campaign_id',
        'title',
        'description',
        'image_url',
        'click_url',
        'type',
        'size',
        'custom_width',
        'custom_height',
        'position',
        'priority',
        'is_active',
        'impressions_count',
        'clicks_count',
        'cost_per_click',
        'cost_per_impression',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'priority' => 'integer',
        'impressions_count' => 'integer',
        'clicks_count' => 'integer',
        'cost_per_click' => 'decimal:4',
        'cost_per_impression' => 'decimal:4',
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }

    /**
     * Get the campaign this advertisement belongs to.
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(AdCampaign::class, 'campaign_id');
    }

    /**
     * Get impressions for this advertisement.
     */
    public function impressions(): HasMany
    {
        return $this->hasMany(AdImpression::class, 'advertisement_id');
    }

    /**
     * Get clicks for this advertisement.
     */
    public function clicks(): HasMany
    {
        return $this->hasMany(AdClick::class, 'advertisement_id');
    }

    /**
     * Scope to get active advertisements.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->whereHas('campaign', function ($q) {
                        $q->active();
                    });
    }

    /**
     * Scope to get advertisements by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get advertisements by position.
     */
    public function scopeByPosition($query, $position)
    {
        return $query->where('position', $position);
    }

    /**
     * Scope to order by priority.
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    /**
     * Get advertisement dimensions.
     */
    public function getDimensionsAttribute(): array
    {
        if ($this->size === 'custom') {
            return [
                'width' => $this->custom_width,
                'height' => $this->custom_height,
            ];
        }

        $dimensions = explode('x', $this->size);
        return [
            'width' => $dimensions[0] ?? null,
            'height' => $dimensions[1] ?? null,
        ];
    }

    /**
     * Get CTR for this advertisement.
     */
    public function getCtrAttribute(): float
    {
        if ($this->impressions_count === 0) {
            return 0;
        }

        return ($this->clicks_count / $this->impressions_count) * 100;
    }

    /**
     * Record an impression.
     */
    public function recordImpression($userId = null, $pageUrl = null, $referrer = null): void
    {
        // Create impression record
        AdImpression::create([
            'advertisement_id' => $this->id,
            'user_id' => $userId,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'page_url' => $pageUrl ?? request()->url(),
            'referrer_url' => $referrer ?? request()->header('referer'),
            'viewed_at' => now(),
        ]);

        // Update counters
        $this->increment('impressions_count');
        $this->campaign->addImpression();

        // Calculate cost if CPM
        if ($this->cost_per_impression > 0) {
            $cost = $this->cost_per_impression / 1000; // CPM is per 1000 impressions
            $this->campaign->addSpent($cost);
        }
    }

    /**
     * Record a click.
     */
    public function recordClick($userId = null, $pageUrl = null, $referrer = null): void
    {
        // Create click record
        AdClick::create([
            'advertisement_id' => $this->id,
            'user_id' => $userId,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'page_url' => $pageUrl ?? request()->url(),
            'referrer_url' => $referrer ?? request()->header('referer'),
            'clicked_at' => now(),
        ]);

        // Update counters
        $this->increment('clicks_count');
        $this->campaign->addClick();

        // Calculate cost if CPC
        if ($this->cost_per_click > 0) {
            $this->campaign->addSpent($this->cost_per_click);
        }
    }

    /**
     * Get available ad types.
     */
    public static function getTypes(): array
    {
        return [
            'banner' => 'Banner Advertisement',
            'sidebar' => 'Sidebar Advertisement',
            'inline' => 'Inline Content Advertisement',
            'popup' => 'Popup Advertisement',
            'sponsored_content' => 'Sponsored Content',
        ];
    }

    /**
     * Get available ad sizes.
     */
    public static function getSizes(): array
    {
        return [
            '728x90' => 'Leaderboard (728x90)',
            '300x250' => 'Medium Rectangle (300x250)',
            '320x50' => 'Mobile Banner (320x50)',
            '160x600' => 'Wide Skyscraper (160x600)',
            '970x250' => 'Billboard (970x250)',
            'custom' => 'Custom Size',
        ];
    }

    /**
     * Get available positions.
     */
    public static function getPositions(): array
    {
        return [
            'header' => 'Header',
            'sidebar' => 'Sidebar',
            'footer' => 'Footer',
            'content_top' => 'Top of Content',
            'content_middle' => 'Middle of Content',
            'content_bottom' => 'Bottom of Content',
        ];
    }

    /**
     * Check if advertisement should be displayed.
     */
    public function shouldDisplay(): bool
    {
        return $this->is_active && 
               $this->campaign->is_active && 
               $this->campaign->is_within_budget;
    }
}
