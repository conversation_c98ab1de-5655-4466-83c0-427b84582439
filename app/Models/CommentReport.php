<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class CommentReport extends Model
{
    use HasFactory, HasUuids;

    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'comment_id',
        'reported_by',
        'reason',
        'description',
        'status',
        'reviewed_by',
        'reviewed_at',
        'review_notes',
        'ip_address',
    ];

    protected $casts = [
        'reviewed_at' => 'datetime',
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = Str::uuid();
        });
    }

    /**
     * Get the comment this report is about.
     */
    public function comment(): BelongsTo
    {
        return $this->belongsTo(Comment::class);
    }

    /**
     * Get the user who reported the comment.
     */
    public function reporter(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reported_by');
    }

    /**
     * Get the user who reviewed the report.
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Scope to get pending reports.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get reviewed reports.
     */
    public function scopeReviewed($query)
    {
        return $query->where('status', 'reviewed');
    }

    /**
     * Mark report as reviewed.
     */
    public function markAsReviewed($reviewerId = null, $notes = null): void
    {
        $this->update([
            'status' => 'reviewed',
            'reviewed_by' => $reviewerId ?? auth()->id(),
            'reviewed_at' => now(),
            'review_notes' => $notes,
        ]);
    }

    /**
     * Mark report as resolved.
     */
    public function markAsResolved($reviewerId = null, $notes = null): void
    {
        $this->update([
            'status' => 'resolved',
            'reviewed_by' => $reviewerId ?? auth()->id(),
            'reviewed_at' => now(),
            'review_notes' => $notes,
        ]);
    }

    /**
     * Dismiss the report.
     */
    public function dismiss($reviewerId = null, $notes = null): void
    {
        $this->update([
            'status' => 'dismissed',
            'reviewed_by' => $reviewerId ?? auth()->id(),
            'reviewed_at' => now(),
            'review_notes' => $notes,
        ]);
    }

    /**
     * Get available report reasons.
     */
    public static function getReasons(): array
    {
        return [
            'spam' => 'Spam or promotional content',
            'inappropriate' => 'Inappropriate or offensive content',
            'harassment' => 'Harassment or bullying',
            'off_topic' => 'Off-topic or irrelevant',
            'other' => 'Other reason',
        ];
    }

    /**
     * Get the human-readable reason.
     */
    public function getReasonLabelAttribute(): string
    {
        $reasons = static::getReasons();
        return $reasons[$this->reason] ?? $this->reason;
    }
}
