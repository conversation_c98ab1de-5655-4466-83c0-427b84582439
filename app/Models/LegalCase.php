<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class LegalCase extends Model
{
    use HasFactory, HasUuids;

    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'case_title',
        'case_number',
        'slug',
        'case_summary',
        'case_details',
        'court_level',
        'court_location',
        'judge_name',
        'case_type',
        'practice_areas',
        'case_status',
        'filing_date',
        'hearing_date',
        'decision_date',
        'plaintiff',
        'defendant',
        'other_parties',
        'plaintiff_lawyers',
        'defendant_lawyers',
        'decision_summary',
        'case_outcome',
        'legal_precedent',
        'cited_cases',
        'judgment_document',
        'case_documents',
        'is_landmark',
        'is_featured',
        'citation_count',
        'meta_title',
        'meta_description',
    ];

    protected $casts = [
        'practice_areas' => 'array',
        'other_parties' => 'array',
        'plaintiff_lawyers' => 'array',
        'defendant_lawyers' => 'array',
        'cited_cases' => 'array',
        'case_documents' => 'array',
        'filing_date' => 'date',
        'hearing_date' => 'date',
        'decision_date' => 'date',
        'is_landmark' => 'boolean',
        'is_featured' => 'boolean',
        'citation_count' => 'integer',
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = Str::uuid();
            if (empty($model->slug)) {
                $model->slug = Str::slug($model->case_title);
            }
        });

        static::updating(function ($model) {
            if ($model->isDirty('case_title') && empty($model->slug)) {
                $model->slug = Str::slug($model->case_title);
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the tags associated with the case.
     */
    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, 'case_tag');
    }

    /**
     * Scope to get only landmark cases.
     */
    public function scopeLandmark($query)
    {
        return $query->where('is_landmark', true);
    }

    /**
     * Scope to get only featured cases.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to filter by court level.
     */
    public function scopeByCourtLevel($query, $courtLevel)
    {
        return $query->where('court_level', $courtLevel);
    }

    /**
     * Scope to filter by case type.
     */
    public function scopeByType($query, $caseType)
    {
        return $query->where('case_type', $caseType);
    }

    /**
     * Scope to filter by case status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('case_status', $status);
    }

    /**
     * Scope to get decided cases.
     */
    public function scopeDecided($query)
    {
        return $query->where('case_status', 'decided');
    }

    /**
     * Scope to get recent cases.
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('decision_date', '>=', now()->subDays($days));
    }

    /**
     * Get the practice areas as a collection of categories.
     */
    public function getPracticeAreaCategoriesAttribute()
    {
        if (!$this->practice_areas) {
            return collect();
        }

        return LegalCategory::whereIn('id', $this->practice_areas)->get();
    }

    /**
     * Get the full case citation.
     */
    public function getFullCitationAttribute(): string
    {
        $year = $this->decision_date ? $this->decision_date->year : '';
        return "{$this->case_title} [{$year}] {$this->case_number}";
    }

    /**
     * Get the case duration in days.
     */
    public function getCaseDurationAttribute(): ?int
    {
        if (!$this->filing_date || !$this->decision_date) {
            return null;
        }

        return $this->filing_date->diffInDays($this->decision_date);
    }

    /**
     * Check if the case is still pending.
     */
    public function getIsPendingAttribute(): bool
    {
        return $this->case_status === 'pending';
    }

    /**
     * Check if the case has been decided.
     */
    public function getIsDecidedAttribute(): bool
    {
        return $this->case_status === 'decided';
    }

    /**
     * Increment the citation count.
     */
    public function incrementCitations(): void
    {
        $this->increment('citation_count');
    }

    /**
     * Get all lawyers involved in the case.
     */
    public function getAllLawyersAttribute()
    {
        $lawyerIds = array_merge(
            $this->plaintiff_lawyers ?? [],
            $this->defendant_lawyers ?? []
        );

        return Lawyer::whereIn('id', array_unique($lawyerIds))->get();
    }

    /**
     * Get Ghana court levels.
     */
    public static function getCourtLevels(): array
    {
        return [
            'supreme_court' => 'Supreme Court',
            'court_of_appeal' => 'Court of Appeal',
            'high_court' => 'High Court',
            'circuit_court' => 'Circuit Court',
            'district_court' => 'District Court',
            'juvenile_court' => 'Juvenile Court',
            'family_tribunal' => 'Family Tribunal',
        ];
    }

    /**
     * Get case types.
     */
    public static function getCaseTypes(): array
    {
        return [
            'civil' => 'Civil',
            'criminal' => 'Criminal',
            'constitutional' => 'Constitutional',
            'commercial' => 'Commercial',
            'family' => 'Family',
            'land' => 'Land',
            'administrative' => 'Administrative',
            'tax' => 'Tax',
            'labor' => 'Labor',
        ];
    }
}
