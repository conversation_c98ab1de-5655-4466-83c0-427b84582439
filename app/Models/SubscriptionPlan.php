<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class SubscriptionPlan extends Model
{
    use HasFactory, HasUuids;

    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'name',
        'slug',
        'description',
        'price',
        'billing_cycle',
        'trial_days',
        'features',
        'is_active',
        'is_featured',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'trial_days' => 'integer',
        'features' => 'array',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'sort_order' => 'integer',
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = Str::uuid();
            if (empty($model->slug)) {
                $model->slug = Str::slug($model->name);
            }
        });
    }

    /**
     * Get subscriptions for this plan.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class, 'plan_id');
    }

    /**
     * Get active subscriptions for this plan.
     */
    public function activeSubscriptions(): HasMany
    {
        return $this->subscriptions()->where('status', 'active');
    }

    /**
     * Scope to get active plans.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get featured plans.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Get formatted price.
     */
    public function getFormattedPriceAttribute(): string
    {
        return 'GH₵ ' . number_format($this->price, 2);
    }

    /**
     * Get billing cycle label.
     */
    public function getBillingCycleLabelAttribute(): string
    {
        return match($this->billing_cycle) {
            'monthly' => 'Monthly',
            'quarterly' => 'Quarterly',
            'yearly' => 'Yearly',
            default => ucfirst($this->billing_cycle),
        };
    }

    /**
     * Get price per month for comparison.
     */
    public function getPricePerMonthAttribute(): float
    {
        return match($this->billing_cycle) {
            'monthly' => $this->price,
            'quarterly' => $this->price / 3,
            'yearly' => $this->price / 12,
            default => $this->price,
        };
    }

    /**
     * Check if plan has trial.
     */
    public function getHasTrialAttribute(): bool
    {
        return $this->trial_days > 0;
    }

    /**
     * Get trial period description.
     */
    public function getTrialDescriptionAttribute(): string
    {
        if ($this->trial_days === 0) {
            return 'No trial';
        }

        if ($this->trial_days === 1) {
            return '1 day trial';
        }

        return "{$this->trial_days} days trial";
    }

    /**
     * Get savings compared to monthly billing.
     */
    public function getSavingsAttribute(): array
    {
        if ($this->billing_cycle === 'monthly') {
            return ['amount' => 0, 'percentage' => 0];
        }

        $monthlyEquivalent = match($this->billing_cycle) {
            'quarterly' => $this->price_per_month * 3,
            'yearly' => $this->price_per_month * 12,
            default => $this->price,
        };

        $savings = $monthlyEquivalent - $this->price;
        $percentage = $monthlyEquivalent > 0 ? ($savings / $monthlyEquivalent) * 100 : 0;

        return [
            'amount' => $savings,
            'percentage' => round($percentage, 1),
        ];
    }

    /**
     * Get plan features as formatted list.
     */
    public function getFormattedFeaturesAttribute(): array
    {
        if (!is_array($this->features)) {
            return [];
        }

        return array_map(function ($feature) {
            return is_string($feature) ? $feature : $feature['name'] ?? '';
        }, $this->features);
    }

    /**
     * Check if plan includes a specific feature.
     */
    public function hasFeature(string $feature): bool
    {
        if (!is_array($this->features)) {
            return false;
        }

        foreach ($this->features as $planFeature) {
            if (is_string($planFeature) && $planFeature === $feature) {
                return true;
            }
            
            if (is_array($planFeature) && ($planFeature['name'] ?? '') === $feature) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get subscriber count.
     */
    public function getSubscriberCountAttribute(): int
    {
        return $this->activeSubscriptions()->count();
    }

    /**
     * Get total revenue from this plan.
     */
    public function getTotalRevenueAttribute(): float
    {
        return $this->subscriptions()
            ->where('status', '!=', 'cancelled')
            ->sum('amount_paid');
    }
}
