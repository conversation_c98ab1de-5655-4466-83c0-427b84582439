<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class Lawyer extends Model
{
    use HasFactory, HasUuids;

    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'first_name',
        'last_name',
        'title',
        'suffix',
        'slug',
        'bio',
        'photo',
        'position',
        'practice_areas',
        'specializations',
        'year_called_to_bar',
        'law_school',
        'other_qualifications',
        'email',
        'phone',
        'direct_line',
        'gba_enrollment_number',
        'gba_verified',
        'law_firm_id',
        'languages',
        'is_active',
        'is_featured',
        'meta_title',
        'meta_description',
    ];

    protected $casts = [
        'practice_areas' => 'array',
        'specializations' => 'array',
        'other_qualifications' => 'array',
        'languages' => 'array',
        'year_called_to_bar' => 'integer',
        'gba_verified' => 'boolean',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = Str::uuid();
            if (empty($model->slug)) {
                $model->slug = Str::slug($model->first_name . ' ' . $model->last_name);
            }
        });

        static::updating(function ($model) {
            if (($model->isDirty('first_name') || $model->isDirty('last_name')) && empty($model->slug)) {
                $model->slug = Str::slug($model->first_name . ' ' . $model->last_name);
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Get the law firm this lawyer belongs to.
     */
    public function lawFirm(): BelongsTo
    {
        return $this->belongsTo(LawFirm::class);
    }

    /**
     * Scope to get only active lawyers.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get only featured lawyers.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to get only verified lawyers.
     */
    public function scopeVerified($query)
    {
        return $query->where('gba_verified', true);
    }

    /**
     * Scope to filter by practice area.
     */
    public function scopeWithPracticeArea($query, $practiceAreaId)
    {
        return $query->whereJsonContains('practice_areas', $practiceAreaId);
    }

    /**
     * Get the full name of the lawyer.
     */
    public function getFullNameAttribute(): string
    {
        $parts = array_filter([
            $this->title,
            $this->first_name,
            $this->last_name,
            $this->suffix,
        ]);

        return implode(' ', $parts);
    }

    /**
     * Get the years of experience.
     */
    public function getYearsOfExperienceAttribute(): ?int
    {
        if (!$this->year_called_to_bar) {
            return null;
        }

        return now()->year - $this->year_called_to_bar;
    }

    /**
     * Get the practice areas as a collection of categories.
     */
    public function getPracticeAreaCategoriesAttribute()
    {
        if (!$this->practice_areas) {
            return collect();
        }

        return LegalCategory::whereIn('id', $this->practice_areas)->get();
    }

    /**
     * Get the initials of the lawyer.
     */
    public function getInitialsAttribute(): string
    {
        return strtoupper(substr($this->first_name, 0, 1) . substr($this->last_name, 0, 1));
    }

    /**
     * Check if the lawyer has a specific practice area.
     */
    public function hasPracticeArea(string $practiceAreaId): bool
    {
        return in_array($practiceAreaId, $this->practice_areas ?? []);
    }

    /**
     * Check if the lawyer speaks a specific language.
     */
    public function speaksLanguage(string $language): bool
    {
        return in_array($language, $this->languages ?? []);
    }

    /**
     * Get common languages in Ghana.
     */
    public static function getGhanaLanguages(): array
    {
        return [
            'English',
            'Twi',
            'Ga',
            'Ewe',
            'Dagbani',
            'Fante',
            'Hausa',
            'Nzema',
            'Gonja',
            'Kasem',
        ];
    }
}
