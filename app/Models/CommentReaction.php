<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class CommentReaction extends Model
{
    use HasFactory, HasUuids;

    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'comment_id',
        'user_id',
        'type',
        'ip_address',
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = Str::uuid();
        });

        static::created(function ($model) {
            // Update comment reaction counts
            $model->updateCommentCounts();
        });

        static::updated(function ($model) {
            // Update comment reaction counts when type changes
            if ($model->isDirty('type')) {
                $model->updateCommentCounts();
            }
        });

        static::deleted(function ($model) {
            // Update comment reaction counts
            $model->updateCommentCounts();
        });
    }

    /**
     * Get the comment this reaction belongs to.
     */
    public function comment(): BelongsTo
    {
        return $this->belongsTo(Comment::class);
    }

    /**
     * Get the user who made this reaction.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Update the comment's reaction counts.
     */
    protected function updateCommentCounts(): void
    {
        $comment = $this->comment;
        
        if ($comment) {
            $likesCount = $comment->reactions()->where('type', 'like')->count();
            $dislikesCount = $comment->reactions()->where('type', 'dislike')->count();
            
            $comment->update([
                'likes_count' => $likesCount,
                'dislikes_count' => $dislikesCount,
            ]);
        }
    }

    /**
     * Toggle user's reaction on a comment.
     */
    public static function toggle($commentId, $type, $userId = null): array
    {
        $userId = $userId ?? auth()->id();
        
        if (!$userId) {
            return ['success' => false, 'message' => 'User not authenticated'];
        }

        $existingReaction = static::where('comment_id', $commentId)
            ->where('user_id', $userId)
            ->first();

        if ($existingReaction) {
            if ($existingReaction->type === $type) {
                // Remove reaction if same type
                $existingReaction->delete();
                return ['success' => true, 'action' => 'removed', 'type' => $type];
            } else {
                // Change reaction type
                $existingReaction->update(['type' => $type]);
                return ['success' => true, 'action' => 'changed', 'type' => $type];
            }
        } else {
            // Create new reaction
            static::create([
                'comment_id' => $commentId,
                'user_id' => $userId,
                'type' => $type,
                'ip_address' => request()->ip(),
            ]);
            return ['success' => true, 'action' => 'added', 'type' => $type];
        }
    }
}
