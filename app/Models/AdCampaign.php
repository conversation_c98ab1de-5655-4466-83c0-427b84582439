<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class AdCampaign extends Model
{
    use HasFactory, HasUuids;

    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'name',
        'slug',
        'description',
        'advertiser_id',
        'status',
        'budget',
        'spent_amount',
        'start_date',
        'end_date',
        'target_categories',
        'target_demographics',
        'impressions_count',
        'clicks_count',
        'ctr',
    ];

    protected $casts = [
        'target_categories' => 'array',
        'target_demographics' => 'array',
        'budget' => 'decimal:2',
        'spent_amount' => 'decimal:2',
        'ctr' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
        'impressions_count' => 'integer',
        'clicks_count' => 'integer',
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->id = Str::uuid();
            if (empty($model->slug)) {
                $model->slug = Str::slug($model->name);
            }
        });
    }

    /**
     * Get the advertiser (user) who owns this campaign.
     */
    public function advertiser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'advertiser_id');
    }

    /**
     * Get the advertisements in this campaign.
     */
    public function advertisements(): HasMany
    {
        return $this->hasMany(Advertisement::class, 'campaign_id');
    }

    /**
     * Get active advertisements in this campaign.
     */
    public function activeAdvertisements(): HasMany
    {
        return $this->advertisements()->where('is_active', true);
    }

    /**
     * Get sponsored content for this campaign.
     */
    public function sponsoredContent(): HasMany
    {
        return $this->hasMany(SponsoredContent::class, 'campaign_id');
    }

    /**
     * Get revenue records for this campaign.
     */
    public function revenue(): HasMany
    {
        return $this->hasMany(AdRevenue::class, 'campaign_id');
    }

    /**
     * Scope to get active campaigns.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('start_date', '<=', now())
                    ->where('end_date', '>=', now());
    }

    /**
     * Scope to get campaigns by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Check if campaign is currently active.
     */
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'active' && 
               $this->start_date <= now() && 
               $this->end_date >= now();
    }

    /**
     * Check if campaign is within budget.
     */
    public function getIsWithinBudgetAttribute(): bool
    {
        if (!$this->budget) {
            return true;
        }
        
        return $this->spent_amount < $this->budget;
    }

    /**
     * Get remaining budget.
     */
    public function getRemainingBudgetAttribute(): float
    {
        if (!$this->budget) {
            return 0;
        }
        
        return max(0, $this->budget - $this->spent_amount);
    }

    /**
     * Calculate and update CTR.
     */
    public function updateCtr(): void
    {
        if ($this->impressions_count > 0) {
            $this->ctr = ($this->clicks_count / $this->impressions_count) * 100;
            $this->save();
        }
    }

    /**
     * Add impression to campaign.
     */
    public function addImpression(): void
    {
        $this->increment('impressions_count');
        $this->updateCtr();
    }

    /**
     * Add click to campaign.
     */
    public function addClick(): void
    {
        $this->increment('clicks_count');
        $this->updateCtr();
    }

    /**
     * Add to spent amount.
     */
    public function addSpent(float $amount): void
    {
        $this->increment('spent_amount', $amount);
        
        // Pause campaign if budget exceeded
        if ($this->budget && $this->spent_amount >= $this->budget) {
            $this->update(['status' => 'paused']);
        }
    }

    /**
     * Get campaign performance metrics.
     */
    public function getPerformanceMetrics(): array
    {
        return [
            'impressions' => $this->impressions_count,
            'clicks' => $this->clicks_count,
            'ctr' => $this->ctr,
            'spent' => $this->spent_amount,
            'remaining_budget' => $this->remaining_budget,
            'cost_per_click' => $this->clicks_count > 0 ? $this->spent_amount / $this->clicks_count : 0,
            'cost_per_impression' => $this->impressions_count > 0 ? $this->spent_amount / $this->impressions_count : 0,
        ];
    }
}
