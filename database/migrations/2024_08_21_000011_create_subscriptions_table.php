<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Subscription plans
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description');
            $table->decimal('price', 8, 2);
            $table->enum('billing_cycle', ['monthly', 'quarterly', 'yearly'])->default('monthly');
            $table->integer('trial_days')->default(0);
            $table->json('features'); // Array of features included
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            $table->index(['is_active', 'sort_order']);
        });

        // User subscriptions
        Schema::create('user_subscriptions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignUuid('plan_id')->constrained('subscription_plans')->onDelete('cascade');
            $table->enum('status', ['active', 'cancelled', 'expired', 'suspended', 'trial'])->default('trial');
            $table->timestamp('starts_at');
            $table->timestamp('ends_at');
            $table->timestamp('trial_ends_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->string('payment_method')->nullable(); // stripe, paypal, mobile_money, etc.
            $table->string('payment_reference')->nullable();
            $table->decimal('amount_paid', 8, 2)->default(0);
            $table->json('subscription_data')->nullable(); // Store payment gateway subscription data
            $table->timestamps();
            
            $table->index(['user_id', 'status']);
            $table->index(['status', 'ends_at']);
        });

        // Payment transactions
        Schema::create('payment_transactions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignUuid('subscription_id')->nullable()->constrained('user_subscriptions')->onDelete('set null');
            $table->string('transaction_reference')->unique();
            $table->string('payment_gateway'); // stripe, paypal, flutterwave, paystack
            $table->string('gateway_transaction_id')->nullable();
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('GHS'); // Ghana Cedis
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled', 'refunded'])->default('pending');
            $table->enum('type', ['subscription', 'one_time', 'refund'])->default('subscription');
            $table->text('description')->nullable();
            $table->json('gateway_response')->nullable(); // Store full gateway response
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();
            
            $table->index(['user_id', 'status']);
            $table->index(['status', 'processed_at']);
            $table->index('transaction_reference');
        });

        // Premium content access
        Schema::create('premium_content_access', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignUuid('article_id')->constrained('articles')->onDelete('cascade');
            $table->timestamp('accessed_at');
            $table->string('access_type'); // subscription, one_time_purchase, free_trial
            $table->timestamps();
            
            $table->unique(['user_id', 'article_id']);
            $table->index(['user_id', 'accessed_at']);
        });

        // Revenue analytics
        Schema::create('revenue_analytics', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->date('date');
            $table->enum('revenue_type', ['subscription', 'advertising', 'sponsored_content', 'one_time']);
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('GHS');
            $table->integer('transactions_count')->default(1);
            $table->json('metadata')->nullable(); // Additional data like plan breakdown, ad performance, etc.
            $table->timestamps();
            
            $table->unique(['date', 'revenue_type']);
            $table->index(['date', 'revenue_type']);
        });

        // Mobile money transactions (for Ghana market)
        Schema::create('mobile_money_transactions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('payment_transaction_id')->constrained('payment_transactions')->onDelete('cascade');
            $table->string('network'); // MTN, Vodafone, AirtelTigo
            $table->string('phone_number');
            $table->string('reference_number')->nullable();
            $table->enum('status', ['pending', 'success', 'failed', 'timeout'])->default('pending');
            $table->text('status_message')->nullable();
            $table->json('provider_response')->nullable();
            $table->timestamps();
            
            $table->index(['network', 'status']);
            $table->index('reference_number');
        });

        // Discount codes and promotions
        Schema::create('discount_codes', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('code')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('type', ['percentage', 'fixed_amount'])->default('percentage');
            $table->decimal('value', 8, 2); // Percentage or fixed amount
            $table->decimal('minimum_amount', 8, 2)->nullable(); // Minimum purchase amount
            $table->integer('usage_limit')->nullable(); // Total usage limit
            $table->integer('usage_count')->default(0);
            $table->integer('user_usage_limit')->default(1); // Per user usage limit
            $table->timestamp('starts_at');
            $table->timestamp('expires_at');
            $table->boolean('is_active')->default(true);
            $table->json('applicable_plans')->nullable(); // Which plans this applies to
            $table->timestamps();
            
            $table->index(['code', 'is_active']);
            $table->index(['starts_at', 'expires_at']);
        });

        // Discount code usage tracking
        Schema::create('discount_code_usage', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('discount_code_id')->constrained('discount_codes')->onDelete('cascade');
            $table->foreignUuid('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignUuid('payment_transaction_id')->constrained('payment_transactions')->onDelete('cascade');
            $table->decimal('discount_amount', 8, 2);
            $table->timestamps();
            
            $table->index(['discount_code_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('discount_code_usage');
        Schema::dropIfExists('discount_codes');
        Schema::dropIfExists('mobile_money_transactions');
        Schema::dropIfExists('revenue_analytics');
        Schema::dropIfExists('premium_content_access');
        Schema::dropIfExists('payment_transactions');
        Schema::dropIfExists('user_subscriptions');
        Schema::dropIfExists('subscription_plans');
    }
};
