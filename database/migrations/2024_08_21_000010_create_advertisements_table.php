<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Advertisement campaigns
        Schema::create('ad_campaigns', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->foreignUuid('advertiser_id')->constrained('users')->onDelete('cascade');
            $table->enum('status', ['draft', 'active', 'paused', 'completed', 'cancelled'])->default('draft');
            $table->decimal('budget', 10, 2)->nullable();
            $table->decimal('spent_amount', 10, 2)->default(0);
            $table->date('start_date');
            $table->date('end_date');
            $table->json('target_categories')->nullable(); // Array of category IDs
            $table->json('target_demographics')->nullable(); // Age, location, etc.
            $table->integer('impressions_count')->default(0);
            $table->integer('clicks_count')->default(0);
            $table->decimal('ctr', 5, 2)->default(0); // Click-through rate
            $table->timestamps();
            
            $table->index(['status', 'start_date', 'end_date']);
        });

        // Individual advertisements
        Schema::create('advertisements', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('campaign_id')->constrained('ad_campaigns')->onDelete('cascade');
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('image_url')->nullable();
            $table->string('click_url');
            $table->enum('type', ['banner', 'sidebar', 'inline', 'popup', 'sponsored_content'])->default('banner');
            $table->enum('size', ['728x90', '300x250', '320x50', '160x600', '970x250', 'custom'])->default('728x90');
            $table->string('custom_width')->nullable();
            $table->string('custom_height')->nullable();
            $table->enum('position', ['header', 'sidebar', 'footer', 'content_top', 'content_middle', 'content_bottom'])->default('sidebar');
            $table->integer('priority')->default(1); // Higher number = higher priority
            $table->boolean('is_active')->default(true);
            $table->integer('impressions_count')->default(0);
            $table->integer('clicks_count')->default(0);
            $table->decimal('cost_per_click', 8, 4)->default(0);
            $table->decimal('cost_per_impression', 8, 4)->default(0);
            $table->timestamps();
            
            $table->index(['type', 'position', 'is_active']);
            $table->index(['campaign_id', 'is_active']);
        });

        // Ad impressions tracking
        Schema::create('ad_impressions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('advertisement_id')->constrained('advertisements')->onDelete('cascade');
            $table->foreignUuid('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('ip_address');
            $table->string('user_agent')->nullable();
            $table->string('page_url');
            $table->string('referrer_url')->nullable();
            $table->timestamp('viewed_at');
            $table->timestamps();
            
            $table->index(['advertisement_id', 'viewed_at']);
            $table->index(['user_id', 'viewed_at']);
        });

        // Ad clicks tracking
        Schema::create('ad_clicks', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('advertisement_id')->constrained('advertisements')->onDelete('cascade');
            $table->foreignUuid('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('ip_address');
            $table->string('user_agent')->nullable();
            $table->string('page_url');
            $table->string('referrer_url')->nullable();
            $table->timestamp('clicked_at');
            $table->timestamps();
            
            $table->index(['advertisement_id', 'clicked_at']);
            $table->index(['user_id', 'clicked_at']);
        });

        // Sponsored content
        Schema::create('sponsored_content', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('campaign_id')->constrained('ad_campaigns')->onDelete('cascade');
            $table->foreignUuid('article_id')->nullable()->constrained('articles')->onDelete('cascade');
            $table->string('title');
            $table->text('excerpt');
            $table->text('content');
            $table->string('featured_image')->nullable();
            $table->string('sponsor_name');
            $table->string('sponsor_logo')->nullable();
            $table->string('call_to_action_text')->default('Learn More');
            $table->string('call_to_action_url');
            $table->boolean('is_active')->default(true);
            $table->integer('views_count')->default(0);
            $table->integer('clicks_count')->default(0);
            $table->timestamps();
            
            $table->index(['is_active', 'created_at']);
        });

        // Revenue tracking
        Schema::create('ad_revenue', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('campaign_id')->constrained('ad_campaigns')->onDelete('cascade');
            $table->foreignUuid('advertisement_id')->nullable()->constrained('advertisements')->onDelete('cascade');
            $table->date('revenue_date');
            $table->decimal('amount', 10, 2);
            $table->enum('type', ['cpc', 'cpm', 'flat_rate', 'sponsored_content']); // Cost per click, cost per mille, flat rate
            $table->integer('impressions')->default(0);
            $table->integer('clicks')->default(0);
            $table->text('notes')->nullable();
            $table->timestamps();
            
            $table->index(['revenue_date', 'type']);
            $table->index(['campaign_id', 'revenue_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ad_revenue');
        Schema::dropIfExists('sponsored_content');
        Schema::dropIfExists('ad_clicks');
        Schema::dropIfExists('ad_impressions');
        Schema::dropIfExists('advertisements');
        Schema::dropIfExists('ad_campaigns');
    }
};
