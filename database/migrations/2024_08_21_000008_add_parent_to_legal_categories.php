<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('legal_categories', function (Blueprint $table) {
            $table->foreignUuid('parent_id')->nullable()->after('id')->constrained('legal_categories')->onDelete('cascade');
            $table->integer('level')->default(0)->after('sort_order'); // 0 = main category, 1 = subcategory, etc.
            $table->string('path')->nullable()->after('level'); // For hierarchical paths like "constitutional-law/human-rights"
            
            $table->index(['parent_id', 'is_active']);
            $table->index(['level', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('legal_categories', function (Blueprint $table) {
            $table->dropForeign(['parent_id']);
            $table->dropColumn(['parent_id', 'level', 'path']);
        });
    }
};
