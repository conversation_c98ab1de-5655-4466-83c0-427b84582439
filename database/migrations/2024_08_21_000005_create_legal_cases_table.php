<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('legal_cases', function (Blueprint $table) {
            $table->uuid('id')->primary()->unique();
            $table->string('case_title');
            $table->string('case_number')->unique();
            $table->string('slug')->unique();
            $table->text('case_summary')->nullable();
            $table->longText('case_details')->nullable();
            
            // Court Information
            $table->enum('court_level', [
                'supreme_court',
                'court_of_appeal', 
                'high_court',
                'circuit_court',
                'district_court',
                'juvenile_court',
                'family_tribunal'
            ]);
            $table->string('court_location')->nullable();
            $table->string('judge_name')->nullable();
            
            // Case Details
            $table->enum('case_type', [
                'civil',
                'criminal',
                'constitutional',
                'commercial',
                'family',
                'land',
                'administrative',
                'tax',
                'labor'
            ]);
            $table->json('practice_areas'); // Related practice areas
            $table->enum('case_status', [
                'pending',
                'decided',
                'appealed',
                'settled',
                'dismissed'
            ])->default('pending');
            
            // Important Dates
            $table->date('filing_date')->nullable();
            $table->date('hearing_date')->nullable();
            $table->date('decision_date')->nullable();
            
            // Parties
            $table->string('plaintiff')->nullable();
            $table->string('defendant')->nullable();
            $table->json('other_parties')->nullable(); // Additional parties
            
            // Legal Representation
            $table->json('plaintiff_lawyers')->nullable(); // Array of lawyer IDs
            $table->json('defendant_lawyers')->nullable(); // Array of lawyer IDs
            
            // Case Outcome
            $table->text('decision_summary')->nullable();
            $table->string('case_outcome')->nullable(); // Won, Lost, Settled, etc.
            $table->text('legal_precedent')->nullable();
            $table->json('cited_cases')->nullable(); // References to other cases
            
            // Documents
            $table->string('judgment_document')->nullable();
            $table->json('case_documents')->nullable(); // Array of document paths
            
            // Significance
            $table->boolean('is_landmark')->default(false);
            $table->boolean('is_featured')->default(false);
            $table->integer('citation_count')->default(0);
            
            // SEO
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['court_level', 'case_status']);
            $table->index(['case_type', 'decision_date']);
            $table->index(['is_landmark', 'is_featured']);
            $table->index('decision_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('legal_cases');
    }
};
