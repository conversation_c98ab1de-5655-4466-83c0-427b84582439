<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lawyers', function (Blueprint $table) {
            $table->uuid('id')->primary()->unique();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('title')->nullable(); // Mr., Mrs., Dr., Prof., etc.
            $table->string('suffix')->nullable(); // Jr., Sr., III, etc.
            $table->string('slug')->unique();
            $table->text('bio')->nullable();
            $table->string('photo')->nullable();
            
            // Professional Information
            $table->string('position')->nullable(); // Partner, Associate, Senior Associate, etc.
            $table->json('practice_areas'); // Array of practice area IDs
            $table->json('specializations')->nullable(); // Specific specializations
            $table->year('year_called_to_bar')->nullable();
            $table->string('law_school')->nullable();
            $table->json('other_qualifications')->nullable(); // Additional degrees, certifications
            
            // Contact Information
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('direct_line')->nullable();
            
            // Ghana Bar Association
            $table->string('gba_enrollment_number')->nullable();
            $table->boolean('gba_verified')->default(false);
            
            // Firm Association
            $table->foreignUuid('law_firm_id')->nullable()->constrained('law_firms')->onDelete('set null');
            
            // Languages
            $table->json('languages')->nullable(); // English, Twi, Ga, Ewe, etc.
            
            // Status
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            
            // SEO
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['law_firm_id', 'is_active']);
            $table->index(['is_active', 'is_featured']);
            $table->index('year_called_to_bar');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lawyers');
    }
};
