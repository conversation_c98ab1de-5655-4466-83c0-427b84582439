<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Newsletter subscriptions
        Schema::create('newsletter_subscriptions', function (Blueprint $table) {
            $table->uuid('id')->primary()->unique();
            $table->string('email')->unique();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->json('interests')->nullable(); // Array of category IDs they're interested in
            $table->enum('frequency', ['daily', 'weekly', 'monthly'])->default('weekly');
            $table->boolean('is_active')->default(true);
            $table->timestamp('verified_at')->nullable();
            $table->string('verification_token')->nullable();
            $table->timestamp('unsubscribed_at')->nullable();
            $table->timestamps();
            
            $table->index(['is_active', 'frequency']);
        });

        // Newsletter campaigns
        Schema::create('newsletter_campaigns', function (Blueprint $table) {
            $table->uuid('id')->primary()->unique();
            $table->string('subject');
            $table->string('slug')->unique();
            $table->longText('content');
            $table->string('template')->default('default');
            $table->enum('status', ['draft', 'scheduled', 'sending', 'sent', 'cancelled'])->default('draft');
            $table->timestamp('scheduled_at')->nullable();
            $table->timestamp('sent_at')->nullable();
            $table->integer('recipients_count')->default(0);
            $table->integer('opened_count')->default(0);
            $table->integer('clicked_count')->default(0);
            $table->json('target_categories')->nullable(); // Which categories to send to
            $table->foreignUuid('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();
            
            $table->index(['status', 'scheduled_at']);
        });

        // Newsletter analytics
        Schema::create('newsletter_analytics', function (Blueprint $table) {
            $table->uuid('id')->primary()->unique();
            $table->foreignUuid('campaign_id')->constrained('newsletter_campaigns')->onDelete('cascade');
            $table->foreignUuid('subscription_id')->constrained('newsletter_subscriptions')->onDelete('cascade');
            $table->enum('action', ['sent', 'opened', 'clicked', 'unsubscribed']);
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->timestamp('action_at');
            $table->timestamps();
            
            $table->index(['campaign_id', 'action']);
            $table->index('action_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('newsletter_analytics');
        Schema::dropIfExists('newsletter_campaigns');
        Schema::dropIfExists('newsletter_subscriptions');
    }
};
