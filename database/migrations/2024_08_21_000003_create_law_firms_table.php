<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('law_firms', function (Blueprint $table) {
            $table->uuid('id')->primary()->unique();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('logo')->nullable();
            $table->json('practice_areas'); // Array of practice area IDs
            
            // Contact Information
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('website')->nullable();
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('region')->nullable(); // Ghana regions
            $table->string('postal_code')->nullable();
            
            // Firm Details
            $table->year('established_year')->nullable();
            $table->integer('number_of_lawyers')->nullable();
            $table->enum('firm_size', ['solo', 'small', 'medium', 'large'])->default('small');
            $table->boolean('is_verified')->default(false);
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_active')->default(true);
            
            // Ghana Bar Association
            $table->string('gba_registration')->nullable();
            $table->boolean('gba_verified')->default(false);
            
            // SEO and Social
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->string('linkedin_url')->nullable();
            $table->string('twitter_url')->nullable();
            $table->string('facebook_url')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['is_active', 'is_verified']);
            $table->index(['city', 'region']);
            $table->index('firm_size');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('law_firms');
    }
};
