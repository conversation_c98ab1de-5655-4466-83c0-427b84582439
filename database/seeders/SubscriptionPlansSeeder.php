<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class SubscriptionPlansSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing plans
        DB::table('subscription_plans')->truncate();

        $plans = [
            [
                'name' => 'Basic Reader',
                'slug' => 'basic-reader',
                'description' => 'Perfect for casual readers who want access to premium legal content and analysis.',
                'price' => 29.99,
                'billing_cycle' => 'monthly',
                'trial_days' => 7,
                'features' => [
                    'Access to all premium articles',
                    'Weekly legal newsletter',
                    'Basic search functionality',
                    'Comment on articles',
                    'Mobile app access',
                    'Email support'
                ],
                'is_active' => true,
                'is_featured' => false,
                'sort_order' => 1,
            ],
            [
                'name' => 'Professional',
                'slug' => 'professional',
                'description' => 'Ideal for legal professionals, law students, and serious legal enthusiasts.',
                'price' => 79.99,
                'billing_cycle' => 'monthly',
                'trial_days' => 14,
                'features' => [
                    'Everything in Basic Reader',
                    'Advanced search with filters',
                    'Download articles as PDF',
                    'Access to legal case database',
                    'Priority email support',
                    'Weekly webinars',
                    'Legal document templates',
                    'Ad-free experience'
                ],
                'is_active' => true,
                'is_featured' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Law Firm',
                'slug' => 'law-firm',
                'description' => 'Comprehensive solution for law firms and legal organizations.',
                'price' => 199.99,
                'billing_cycle' => 'monthly',
                'trial_days' => 30,
                'features' => [
                    'Everything in Professional',
                    'Multi-user access (up to 10 users)',
                    'Custom legal alerts',
                    'API access for integration',
                    'Dedicated account manager',
                    'Phone support',
                    'Custom research requests',
                    'White-label options',
                    'Analytics dashboard',
                    'Bulk download capabilities'
                ],
                'is_active' => true,
                'is_featured' => false,
                'sort_order' => 3,
            ],
            [
                'name' => 'Student',
                'slug' => 'student',
                'description' => 'Special pricing for law students and recent graduates.',
                'price' => 19.99,
                'billing_cycle' => 'monthly',
                'trial_days' => 14,
                'features' => [
                    'Access to all premium articles',
                    'Study guides and summaries',
                    'Case law database access',
                    'Student discussion forums',
                    'Career guidance resources',
                    'Exam preparation materials',
                    'Mobile app access'
                ],
                'is_active' => true,
                'is_featured' => false,
                'sort_order' => 4,
            ],
            [
                'name' => 'Professional Quarterly',
                'slug' => 'professional-quarterly',
                'description' => 'Professional plan with quarterly billing - save 15%!',
                'price' => 203.97, // 79.99 * 3 * 0.85
                'billing_cycle' => 'quarterly',
                'trial_days' => 14,
                'features' => [
                    'Everything in Professional plan',
                    'Save 15% with quarterly billing',
                    'Advanced search with filters',
                    'Download articles as PDF',
                    'Access to legal case database',
                    'Priority email support',
                    'Weekly webinars',
                    'Legal document templates',
                    'Ad-free experience'
                ],
                'is_active' => true,
                'is_featured' => false,
                'sort_order' => 5,
            ],
            [
                'name' => 'Professional Annual',
                'slug' => 'professional-annual',
                'description' => 'Professional plan with annual billing - save 25%!',
                'price' => 719.91, // 79.99 * 12 * 0.75
                'billing_cycle' => 'yearly',
                'trial_days' => 30,
                'features' => [
                    'Everything in Professional plan',
                    'Save 25% with annual billing',
                    'Advanced search with filters',
                    'Download articles as PDF',
                    'Access to legal case database',
                    'Priority email support',
                    'Weekly webinars',
                    'Legal document templates',
                    'Ad-free experience',
                    'Exclusive annual subscriber events'
                ],
                'is_active' => true,
                'is_featured' => true,
                'sort_order' => 6,
            ],
        ];

        foreach ($plans as $index => $planData) {
            DB::table('subscription_plans')->insert([
                'id' => Str::uuid(),
                'name' => $planData['name'],
                'slug' => $planData['slug'],
                'description' => $planData['description'],
                'price' => $planData['price'],
                'billing_cycle' => $planData['billing_cycle'],
                'trial_days' => $planData['trial_days'],
                'features' => json_encode($planData['features']),
                'is_active' => $planData['is_active'],
                'is_featured' => $planData['is_featured'],
                'sort_order' => $planData['sort_order'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
