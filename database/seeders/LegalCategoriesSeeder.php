<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class LegalCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Constitutional Law',
                'slug' => 'constitutional-law',
                'description' => 'Constitutional interpretation, fundamental rights, and governance issues in Ghana',
                'color' => '#dc2626',
                'icon' => 'ti-book',
                'sort_order' => 1,
                'is_featured' => true,
            ],
            [
                'name' => 'Commercial Law',
                'slug' => 'commercial-law',
                'description' => 'Business law, corporate governance, and commercial transactions',
                'color' => '#059669',
                'icon' => 'ti-briefcase',
                'sort_order' => 2,
                'is_featured' => true,
            ],
            [
                'name' => 'Criminal Law',
                'slug' => 'criminal-law',
                'description' => 'Criminal justice, prosecutions, and criminal procedure in Ghana',
                'color' => '#dc2626',
                'icon' => 'ti-shield',
                'sort_order' => 3,
                'is_featured' => true,
            ],
            [
                'name' => 'Family Law',
                'slug' => 'family-law',
                'description' => 'Marriage, divorce, child custody, and family matters',
                'color' => '#7c3aed',
                'icon' => 'ti-heart',
                'sort_order' => 4,
                'is_featured' => false,
            ],
            [
                'name' => 'Land Law',
                'slug' => 'land-law',
                'description' => 'Property rights, land acquisition, and real estate law',
                'color' => '#ea580c',
                'icon' => 'ti-home',
                'sort_order' => 5,
                'is_featured' => true,
            ],
            [
                'name' => 'Labor & Employment',
                'slug' => 'labor-employment',
                'description' => 'Employment law, labor relations, and workplace rights',
                'color' => '#0891b2',
                'icon' => 'ti-users',
                'sort_order' => 6,
                'is_featured' => false,
            ],
            [
                'name' => 'Tax Law',
                'slug' => 'tax-law',
                'description' => 'Taxation, revenue law, and tax disputes',
                'color' => '#ca8a04',
                'icon' => 'ti-calculator',
                'sort_order' => 7,
                'is_featured' => false,
            ],
            [
                'name' => 'Banking & Finance',
                'slug' => 'banking-finance',
                'description' => 'Financial services, banking regulations, and securities law',
                'color' => '#16a34a',
                'icon' => 'ti-credit-card',
                'sort_order' => 8,
                'is_featured' => true,
            ],
            [
                'name' => 'Environmental Law',
                'slug' => 'environmental-law',
                'description' => 'Environmental protection, mining law, and natural resources',
                'color' => '#15803d',
                'icon' => 'ti-leaf',
                'sort_order' => 9,
                'is_featured' => false,
            ],
            [
                'name' => 'Intellectual Property',
                'slug' => 'intellectual-property',
                'description' => 'Patents, trademarks, copyrights, and IP protection',
                'color' => '#9333ea',
                'icon' => 'ti-lightbulb',
                'sort_order' => 10,
                'is_featured' => false,
            ],
            [
                'name' => 'Administrative Law',
                'slug' => 'administrative-law',
                'description' => 'Government administration, public service, and regulatory law',
                'color' => '#4f46e5',
                'icon' => 'ti-settings',
                'sort_order' => 11,
                'is_featured' => false,
            ],
            [
                'name' => 'Human Rights',
                'slug' => 'human-rights',
                'description' => 'Human rights advocacy, civil liberties, and social justice',
                'color' => '#e11d48',
                'icon' => 'ti-flag',
                'sort_order' => 12,
                'is_featured' => true,
            ],
            [
                'name' => 'International Law',
                'slug' => 'international-law',
                'description' => 'International treaties, trade law, and cross-border legal issues',
                'color' => '#0369a1',
                'icon' => 'ti-world',
                'sort_order' => 13,
                'is_featured' => false,
            ],
            [
                'name' => 'Technology Law',
                'slug' => 'technology-law',
                'description' => 'Cybersecurity, data protection, and technology regulations',
                'color' => '#6366f1',
                'icon' => 'ti-computer',
                'sort_order' => 14,
                'is_featured' => false,
            ],
            [
                'name' => 'Healthcare Law',
                'slug' => 'healthcare-law',
                'description' => 'Medical law, healthcare regulations, and patient rights',
                'color' => '#dc2626',
                'icon' => 'ti-heart-broken',
                'sort_order' => 15,
                'is_featured' => false,
            ],
        ];

        foreach ($categories as $category) {
            DB::table('legal_categories')->insert([
                'id' => Str::uuid(),
                'name' => $category['name'],
                'slug' => $category['slug'],
                'description' => $category['description'],
                'color' => $category['color'],
                'icon' => $category['icon'],
                'sort_order' => $category['sort_order'],
                'is_active' => true,
                'is_featured' => $category['is_featured'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
