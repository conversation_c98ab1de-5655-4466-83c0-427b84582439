<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class DeepLegalCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing categories
        DB::table('legal_categories')->truncate();

        $categories = [
            [
                'name' => 'Constitutional Law',
                'slug' => 'constitutional-law',
                'description' => 'Constitutional interpretation, fundamental rights, and governance issues in Ghana',
                'color' => '#dc2626',
                'icon' => 'ti-book',
                'sort_order' => 1,
                'is_featured' => true,
                'subcategories' => [
                    [
                        'name' => 'Fundamental Human Rights',
                        'slug' => 'fundamental-human-rights',
                        'description' => 'Basic human rights and freedoms under Ghana\'s constitution',
                        'color' => '#dc2626',
                        'icon' => 'ti-user',
                    ],
                    [
                        'name' => 'Directive Principles of State Policy',
                        'slug' => 'directive-principles',
                        'description' => 'State policy guidelines and objectives',
                        'color' => '#dc2626',
                        'icon' => 'ti-flag',
                    ],
                    [
                        'name' => 'Separation of Powers',
                        'slug' => 'separation-of-powers',
                        'description' => 'Executive, legislative, and judicial powers',
                        'color' => '#dc2626',
                        'icon' => 'ti-layout',
                    ],
                    [
                        'name' => 'Constitutional Amendments',
                        'slug' => 'constitutional-amendments',
                        'description' => 'Constitutional review and amendment processes',
                        'color' => '#dc2626',
                        'icon' => 'ti-edit',
                    ],
                ]
            ],
            [
                'name' => 'Commercial Law',
                'slug' => 'commercial-law',
                'description' => 'Business law, corporate governance, and commercial transactions',
                'color' => '#059669',
                'icon' => 'ti-briefcase',
                'sort_order' => 2,
                'is_featured' => true,
                'subcategories' => [
                    [
                        'name' => 'Corporate Law',
                        'slug' => 'corporate-law',
                        'description' => 'Company formation, governance, and corporate compliance',
                        'color' => '#059669',
                        'icon' => 'ti-building',
                        'subcategories' => [
                            [
                                'name' => 'Company Formation',
                                'slug' => 'company-formation',
                                'description' => 'Business registration and incorporation procedures',
                                'color' => '#059669',
                                'icon' => 'ti-plus',
                            ],
                            [
                                'name' => 'Corporate Governance',
                                'slug' => 'corporate-governance',
                                'description' => 'Board responsibilities and corporate management',
                                'color' => '#059669',
                                'icon' => 'ti-users',
                            ],
                            [
                                'name' => 'Mergers & Acquisitions',
                                'slug' => 'mergers-acquisitions',
                                'description' => 'Corporate mergers, acquisitions, and restructuring',
                                'color' => '#059669',
                                'icon' => 'ti-git-merge',
                            ],
                        ]
                    ],
                    [
                        'name' => 'Contract Law',
                        'slug' => 'contract-law',
                        'description' => 'Commercial contracts and agreement enforcement',
                        'color' => '#059669',
                        'icon' => 'ti-file-text',
                    ],
                    [
                        'name' => 'Securities Law',
                        'slug' => 'securities-law',
                        'description' => 'Stock market regulations and securities trading',
                        'color' => '#059669',
                        'icon' => 'ti-trending-up',
                    ],
                    [
                        'name' => 'Competition Law',
                        'slug' => 'competition-law',
                        'description' => 'Anti-trust and fair competition regulations',
                        'color' => '#059669',
                        'icon' => 'ti-target',
                    ],
                ]
            ],
            [
                'name' => 'Criminal Law',
                'slug' => 'criminal-law',
                'description' => 'Criminal justice, prosecutions, and criminal procedure in Ghana',
                'color' => '#dc2626',
                'icon' => 'ti-shield',
                'sort_order' => 3,
                'is_featured' => true,
                'subcategories' => [
                    [
                        'name' => 'Criminal Procedure',
                        'slug' => 'criminal-procedure',
                        'description' => 'Court procedures and criminal justice process',
                        'color' => '#dc2626',
                        'icon' => 'ti-clipboard',
                    ],
                    [
                        'name' => 'White Collar Crime',
                        'slug' => 'white-collar-crime',
                        'description' => 'Financial crimes, fraud, and corruption cases',
                        'color' => '#dc2626',
                        'icon' => 'ti-credit-card',
                    ],
                    [
                        'name' => 'Cybercrime',
                        'slug' => 'cybercrime',
                        'description' => 'Internet crimes and digital security offenses',
                        'color' => '#dc2626',
                        'icon' => 'ti-wifi',
                    ],
                    [
                        'name' => 'Drug Offenses',
                        'slug' => 'drug-offenses',
                        'description' => 'Narcotics and controlled substances violations',
                        'color' => '#dc2626',
                        'icon' => 'ti-alert-triangle',
                    ],
                ]
            ],
            [
                'name' => 'Family Law',
                'slug' => 'family-law',
                'description' => 'Marriage, divorce, child custody, and family matters',
                'color' => '#7c3aed',
                'icon' => 'ti-heart',
                'sort_order' => 4,
                'is_featured' => false,
                'subcategories' => [
                    [
                        'name' => 'Marriage & Divorce',
                        'slug' => 'marriage-divorce',
                        'description' => 'Marriage ceremonies, divorce proceedings, and separation',
                        'color' => '#7c3aed',
                        'icon' => 'ti-heart-broken',
                    ],
                    [
                        'name' => 'Child Custody',
                        'slug' => 'child-custody',
                        'description' => 'Child custody, support, and welfare matters',
                        'color' => '#7c3aed',
                        'icon' => 'ti-user-plus',
                    ],
                    [
                        'name' => 'Adoption',
                        'slug' => 'adoption',
                        'description' => 'Child adoption procedures and requirements',
                        'color' => '#7c3aed',
                        'icon' => 'ti-users',
                    ],
                    [
                        'name' => 'Domestic Violence',
                        'slug' => 'domestic-violence',
                        'description' => 'Domestic abuse cases and protection orders',
                        'color' => '#7c3aed',
                        'icon' => 'ti-shield-off',
                    ],
                ]
            ],
            [
                'name' => 'Land Law',
                'slug' => 'land-law',
                'description' => 'Property rights, land acquisition, and real estate law',
                'color' => '#ea580c',
                'icon' => 'ti-home',
                'sort_order' => 5,
                'is_featured' => true,
                'subcategories' => [
                    [
                        'name' => 'Land Acquisition',
                        'slug' => 'land-acquisition',
                        'description' => 'Land purchase, transfer, and acquisition procedures',
                        'color' => '#ea580c',
                        'icon' => 'ti-map',
                    ],
                    [
                        'name' => 'Property Rights',
                        'slug' => 'property-rights',
                        'description' => 'Ownership rights and property disputes',
                        'color' => '#ea580c',
                        'icon' => 'ti-key',
                    ],
                    [
                        'name' => 'Real Estate Development',
                        'slug' => 'real-estate-development',
                        'description' => 'Property development and construction law',
                        'color' => '#ea580c',
                        'icon' => 'ti-building',
                    ],
                    [
                        'name' => 'Land Disputes',
                        'slug' => 'land-disputes',
                        'description' => 'Boundary disputes and land conflicts',
                        'color' => '#ea580c',
                        'icon' => 'ti-alert-circle',
                    ],
                ]
            ],
            [
                'name' => 'Labor & Employment',
                'slug' => 'labor-employment',
                'description' => 'Employment law, labor relations, and workplace rights',
                'color' => '#0891b2',
                'icon' => 'ti-users',
                'sort_order' => 6,
                'is_featured' => false,
                'subcategories' => [
                    [
                        'name' => 'Employment Contracts',
                        'slug' => 'employment-contracts',
                        'description' => 'Employment agreements and contract terms',
                        'color' => '#0891b2',
                        'icon' => 'ti-file-text',
                    ],
                    [
                        'name' => 'Workplace Safety',
                        'slug' => 'workplace-safety',
                        'description' => 'Occupational health and safety regulations',
                        'color' => '#0891b2',
                        'icon' => 'ti-shield',
                    ],
                    [
                        'name' => 'Labor Disputes',
                        'slug' => 'labor-disputes',
                        'description' => 'Industrial relations and labor conflicts',
                        'color' => '#0891b2',
                        'icon' => 'ti-alert-triangle',
                    ],
                    [
                        'name' => 'Workers\' Rights',
                        'slug' => 'workers-rights',
                        'description' => 'Employee rights and workplace protections',
                        'color' => '#0891b2',
                        'icon' => 'ti-user-check',
                    ],
                ]
            ],
        ];

        $this->createCategories($categories);
    }

    private function createCategories($categories, $parentId = null, $level = 0, $parentPath = '')
    {
        foreach ($categories as $index => $categoryData) {
            $categoryId = Str::uuid();
            $path = $parentPath ? $parentPath . '/' . $categoryData['slug'] : $categoryData['slug'];
            
            DB::table('legal_categories')->insert([
                'id' => $categoryId,
                'parent_id' => $parentId,
                'name' => $categoryData['name'],
                'slug' => $categoryData['slug'],
                'description' => $categoryData['description'],
                'color' => $categoryData['color'],
                'icon' => $categoryData['icon'],
                'sort_order' => $categoryData['sort_order'] ?? $index + 1,
                'level' => $level,
                'path' => $path,
                'is_active' => true,
                'is_featured' => $categoryData['is_featured'] ?? false,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Create subcategories if they exist
            if (isset($categoryData['subcategories'])) {
                $this->createCategories($categoryData['subcategories'], $categoryId, $level + 1, $path);
            }
        }
    }
}
