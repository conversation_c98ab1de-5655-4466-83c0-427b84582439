@extends('layouts.app.master')
@section('title')
    Admin Dashboard - Ghana Legal News
@endsection

@section('content')
    <div class="page-body">
        <div class="container-fluid">
            <div class="page-title">
                <div class="row">
                    <div class="col-sm-6">
                        <h3>Legal News Dashboard</h3>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.dashboard') }}">
                                    <svg class="stroke-icon">
                                        <use href="../assets/svg/icon-sprite.svg#stroke-home"></use>
                                    </svg>
                                </a>
                            </li>
                            <li class="breadcrumb-item active">Dashboard</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- Container-fluid starts-->
        <div class="container-fluid default-dashboard">
            <!-- Statistics Cards Row -->
            <div class="row widget-grid">
                <!-- Welcome Card -->
                <div class="col-xxl-4 col-sm-6 box-col-6">
                    <div class="card profile-box">
                        <div class="card-body">
                            <div class="d-flex media-wrapper justify-content-between">
                                <div class="flex-grow-1">
                                    <div class="greeting-user">
                                        <h2 class="f-w-600">Welcome {{ auth()->user()->name }}!</h2>
                                        <p>Here's what's happening with Ghana Legal News today</p>
                                        <div class="whatsnew-btn">
                                            <a class="btn btn-outline-white" href="{{ route('admin.articles.create') }}">
                                                Create Article
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="clockbox">
                                        <svg id="clock" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 600 600">
                                            <g id="face">
                                                <circle class="circle" cx="300" cy="300" r="253.9"></circle>
                                                <path class="hour-marks" d="M300.5 94V61M506 300.5h32M300.5 506v33M94 300.5H60M411.3 107.8l7.9-13.8M493 190.2l13-7.4M492.1 411.4l16.5 9.5M411 492.3l8.9 15.3M189 492.3l-9.2 15.9M107.7 411L93 419.5M107.5 189.3l-17.1-9.9M188.1 108.2l-9-15.6"></path>
                                                <circle class="mid-circle" cx="300" cy="300" r="16.2"></circle>
                                            </g>
                                            <g id="hour" style="transform-origin: 300px 300px">
                                                <path id="hour-arm" d="M300.5 298V142"></path>
                                            </g>
                                            <g id="minute" style="transform-origin: 300px 300px">
                                                <path id="minute-arm" d="M300.5 298V67"></path>
                                            </g>
                                            <g id="second" style="transform-origin: 300px 300px">
                                                <path id="second-arm" d="M300.5 350V55"></path>
                                            </g>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Articles Stats -->
                <div class="col-xxl-2 col-sm-6 box-col-3">
                    <div class="card widget-1">
                        <div class="card-body">
                            <div class="widget-content">
                                <div class="widget-round primary">
                                    <div class="bg-round">
                                        <svg class="svg-fill">
                                            <use href="../assets/svg/icon-sprite.svg#new-order"></use>
                                        </svg>
                                    </div>
                                </div>
                                <div>
                                    <h4>{{ number_format($stats['total_articles']) }}</h4>
                                    <span class="f-light">Total Articles</span>
                                </div>
                            </div>
                            <div class="font-primary f-w-500">
                                <i class="icon-arrow-up"></i>
                                <span class="txt-primary">{{ $stats['published_articles'] }} Published</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Categories Stats -->
                <div class="col-xxl-2 col-sm-6 box-col-3">
                    <div class="card widget-1">
                        <div class="card-body">
                            <div class="widget-content">
                                <div class="widget-round secondary">
                                    <div class="bg-round">
                                        <svg class="svg-fill">
                                            <use href="../assets/svg/icon-sprite.svg#customers"></use>
                                        </svg>
                                    </div>
                                </div>
                                <div>
                                    <h4>{{ number_format($stats['total_categories']) }}</h4>
                                    <span class="f-light">Legal Categories</span>
                                </div>
                            </div>
                            <div class="font-secondary f-w-500">
                                <i class="icon-arrow-up"></i>
                                <span class="txt-secondary">{{ $stats['active_categories'] }} Active</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Law Firms Stats -->
                <div class="col-xxl-2 col-sm-6 box-col-3">
                    <div class="card widget-1">
                        <div class="card-body">
                            <div class="widget-content">
                                <div class="widget-round success">
                                    <div class="bg-round">
                                        <svg class="svg-fill">
                                            <use href="../assets/svg/icon-sprite.svg#rate"></use>
                                        </svg>
                                    </div>
                                </div>
                                <div>
                                    <h4>{{ number_format($stats['total_law_firms']) }}</h4>
                                    <span class="f-light">Law Firms</span>
                                </div>
                            </div>
                            <div class="font-success f-w-500">
                                <i class="icon-arrow-up"></i>
                                <span class="txt-success">{{ $stats['verified_law_firms'] }} Verified</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Legal Cases Stats -->
                <div class="col-xxl-2 col-sm-6 box-col-3">
                    <div class="card widget-1">
                        <div class="card-body">
                            <div class="widget-content">
                                <div class="widget-round warning">
                                    <div class="bg-round">
                                        <svg class="svg-fill">
                                            <use href="../assets/svg/icon-sprite.svg#profit"></use>
                                        </svg>
                                    </div>
                                </div>
                                <div>
                                    <h4>{{ number_format($stats['total_cases']) }}</h4>
                                    <span class="f-light">Legal Cases</span>
                                </div>
                            </div>
                            <div class="font-warning f-w-500">
                                <i class="icon-arrow-up"></i>
                                <span class="txt-warning">{{ $stats['landmark_cases'] }} Landmark</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Management Row -->
            <div class="row">
                <!-- Recent Articles -->
                <div class="col-xl-6 col-lg-12">
                    <div class="card">
                        <div class="card-header">
                            <h4>Recent Articles</h4>
                            <div class="card-header-right">
                                <a href="{{ route('admin.articles.index') }}" class="btn btn-primary btn-sm">
                                    View All
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            @if($recentArticles->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Title</th>
                                                <th>Author</th>
                                                <th>Category</th>
                                                <th>Status</th>
                                                <th>Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($recentArticles as $article)
                                                <tr>
                                                    <td>
                                                        <a href="{{ route('admin.articles.show', $article) }}" class="text-decoration-none">
                                                            {{ Str::limit($article->title, 40) }}
                                                        </a>
                                                        @if($article->is_featured)
                                                            <span class="badge badge-warning badge-sm ms-1">Featured</span>
                                                        @endif
                                                        @if($article->is_breaking)
                                                            <span class="badge badge-danger badge-sm ms-1">Breaking</span>
                                                        @endif
                                                    </td>
                                                    <td>{{ $article->author->name }}</td>
                                                    <td>
                                                        <span class="badge" style="background-color: {{ $article->category->color }}">
                                                            {{ $article->category->name }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-{{ $article->status === 'published' ? 'success' : ($article->status === 'draft' ? 'secondary' : 'warning') }}">
                                                            {{ ucfirst($article->status) }}
                                                        </span>
                                                    </td>
                                                    <td>{{ $article->created_at->format('M j, Y') }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <p class="text-muted">No articles found. <a href="{{ route('admin.articles.create') }}">Create your first article</a></p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Popular Categories -->
                <div class="col-xl-6 col-lg-12">
                    <div class="card">
                        <div class="card-header">
                            <h4>Popular Categories</h4>
                            <div class="card-header-right">
                                <a href="{{ route('admin.categories.index') }}" class="btn btn-primary btn-sm">
                                    Manage Categories
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            @if($popularCategories->count() > 0)
                                <div class="list-group list-group-flush">
                                    @foreach($popularCategories as $category)
                                        <div class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0">
                                                    <div class="avatar-sm rounded" style="background-color: {{ $category->color }}20; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                                        <i class="{{ $category->icon ?? 'ti-folder' }}" style="color: {{ $category->color }}"></i>
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <h6 class="mb-0">{{ $category->name }}</h6>
                                                    <small class="text-muted">{{ Str::limit($category->description, 50) }}</small>
                                                </div>
                                            </div>
                                            <span class="badge badge-primary">{{ $category->published_articles_count }} articles</span>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <p class="text-muted">No categories found.</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Row -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4>Quick Actions</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <a href="{{ route('admin.articles.create') }}" class="btn btn-primary btn-lg w-100">
                                        <i class="fa fa-plus me-2"></i>
                                        Create Article
                                    </a>
                                </div>
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <a href="{{ route('admin.categories.create') }}" class="btn btn-secondary btn-lg w-100">
                                        <i class="fa fa-folder-plus me-2"></i>
                                        Add Category
                                    </a>
                                </div>
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <a href="{{ route('admin.articles.index', ['status' => 'draft']) }}" class="btn btn-warning btn-lg w-100">
                                        <i class="fa fa-edit me-2"></i>
                                        Review Drafts
                                    </a>
                                </div>
                                <div class="col-md-3 col-sm-6 mb-3">
                                    <a href="{{ route('admin.articles.index', ['is_breaking' => 1]) }}" class="btn btn-danger btn-lg w-100">
                                        <i class="fa fa-bolt me-2"></i>
                                        Breaking News
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Container-fluid Ends-->
    </div>
@endsection
