@extends('layouts.app.master')
@section('title')
    Articles Management - Ghana Legal News
@endsection

@section('content')
    <div class="page-body">
        <div class="container-fluid">
            <div class="page-title">
                <div class="row">
                    <div class="col-sm-6">
                        <h3>Articles Management</h3>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.dashboard') }}">
                                    <svg class="stroke-icon">
                                        <use href="../assets/svg/icon-sprite.svg#stroke-home"></use>
                                    </svg>
                                </a>
                            </li>
                            <li class="breadcrumb-item">Content</li>
                            <li class="breadcrumb-item active">Articles</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h4>All Articles</h4>
                                </div>
                                <div class="col-md-6 text-end">
                                    <a href="{{ route('admin.articles.create') }}" class="btn btn-primary">
                                        <i class="fa fa-plus me-2"></i>Create Article
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="card-body">
                            <!-- Filters -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <form method="GET" action="{{ route('admin.articles.index') }}" class="row g-3">
                                        <div class="col-md-3">
                                            <label class="form-label">Search</label>
                                            <input type="text" name="search" class="form-control" 
                                                   placeholder="Search articles..." value="{{ request('search') }}">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Status</label>
                                            <select name="status" class="form-select">
                                                <option value="">All Status</option>
                                                <option value="draft" {{ request('status') === 'draft' ? 'selected' : '' }}>Draft</option>
                                                <option value="published" {{ request('status') === 'published' ? 'selected' : '' }}>Published</option>
                                                <option value="scheduled" {{ request('status') === 'scheduled' ? 'selected' : '' }}>Scheduled</option>
                                                <option value="archived" {{ request('status') === 'archived' ? 'selected' : '' }}>Archived</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">Category</label>
                                            <select name="category" class="form-select">
                                                <option value="">All Categories</option>
                                                @foreach($categories as $category)
                                                    <option value="{{ $category->id }}" {{ request('category') === $category->id ? 'selected' : '' }}>
                                                        {{ $category->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Type</label>
                                            <select name="type" class="form-select">
                                                <option value="">All Types</option>
                                                <option value="news" {{ request('type') === 'news' ? 'selected' : '' }}>News</option>
                                                <option value="analysis" {{ request('type') === 'analysis' ? 'selected' : '' }}>Analysis</option>
                                                <option value="opinion" {{ request('type') === 'opinion' ? 'selected' : '' }}>Opinion</option>
                                                <option value="case_study" {{ request('type') === 'case_study' ? 'selected' : '' }}>Case Study</option>
                                                <option value="interview" {{ request('type') === 'interview' ? 'selected' : '' }}>Interview</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">&nbsp;</label>
                                            <div class="d-flex gap-2">
                                                <button type="submit" class="btn btn-primary">Filter</button>
                                                <a href="{{ route('admin.articles.index') }}" class="btn btn-secondary">Clear</a>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Articles Table -->
                            @if($articles->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Title</th>
                                                <th>Author</th>
                                                <th>Category</th>
                                                <th>Type</th>
                                                <th>Status</th>
                                                <th>Views</th>
                                                <th>Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($articles as $article)
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-start">
                                                            @if($article->featured_image)
                                                                <img src="{{ Storage::url($article->featured_image) }}" 
                                                                     alt="{{ $article->title }}" 
                                                                     class="rounded me-3" 
                                                                     style="width: 50px; height: 50px; object-fit: cover;">
                                                            @endif
                                                            <div>
                                                                <h6 class="mb-1">
                                                                    <a href="{{ route('admin.articles.show', $article) }}" class="text-decoration-none">
                                                                        {{ Str::limit($article->title, 50) }}
                                                                    </a>
                                                                </h6>
                                                                <div class="d-flex gap-1">
                                                                    @if($article->is_featured)
                                                                        <span class="badge badge-warning badge-sm">Featured</span>
                                                                    @endif
                                                                    @if($article->is_breaking)
                                                                        <span class="badge badge-danger badge-sm">Breaking</span>
                                                                    @endif
                                                                    @if($article->is_premium)
                                                                        <span class="badge badge-info badge-sm">Premium</span>
                                                                    @endif
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>{{ $article->author->name }}</td>
                                                    <td>
                                                        <span class="badge" style="background-color: {{ $article->category->color }}">
                                                            {{ $article->category->name }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-light">{{ ucfirst(str_replace('_', ' ', $article->type)) }}</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-{{ $article->status === 'published' ? 'success' : ($article->status === 'draft' ? 'secondary' : 'warning') }}">
                                                            {{ ucfirst($article->status) }}
                                                        </span>
                                                    </td>
                                                    <td>{{ number_format($article->views_count) }}</td>
                                                    <td>
                                                        <div>
                                                            <small class="text-muted">Created:</small><br>
                                                            {{ $article->created_at->format('M j, Y') }}
                                                        </div>
                                                        @if($article->published_at)
                                                            <div>
                                                                <small class="text-muted">Published:</small><br>
                                                                {{ $article->published_at->format('M j, Y') }}
                                                            </div>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="{{ route('admin.articles.show', $article) }}" 
                                                               class="btn btn-sm btn-outline-primary" title="View">
                                                                <i class="fa fa-eye"></i>
                                                            </a>
                                                            <a href="{{ route('admin.articles.edit', $article) }}" 
                                                               class="btn btn-sm btn-outline-secondary" title="Edit">
                                                                <i class="fa fa-edit"></i>
                                                            </a>
                                                            <form method="POST" action="{{ route('admin.articles.destroy', $article) }}" 
                                                                  class="d-inline" onsubmit="return confirm('Are you sure you want to delete this article?')">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                                    <i class="fa fa-trash"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                <div class="d-flex justify-content-between align-items-center mt-4">
                                    <div>
                                        <p class="text-muted">
                                            Showing {{ $articles->firstItem() }} to {{ $articles->lastItem() }} of {{ $articles->total() }} articles
                                        </p>
                                    </div>
                                    <div>
                                        {{ $articles->appends(request()->query())->links() }}
                                    </div>
                                </div>
                            @else
                                <div class="text-center py-5">
                                    <div class="mb-3">
                                        <i class="fa fa-newspaper-o fa-3x text-muted"></i>
                                    </div>
                                    <h5>No Articles Found</h5>
                                    <p class="text-muted">
                                        @if(request()->hasAny(['search', 'status', 'category', 'type']))
                                            No articles match your current filters. <a href="{{ route('admin.articles.index') }}">Clear filters</a> to see all articles.
                                        @else
                                            You haven't created any articles yet.
                                        @endif
                                    </p>
                                    <a href="{{ route('admin.articles.create') }}" class="btn btn-primary">
                                        <i class="fa fa-plus me-2"></i>Create Your First Article
                                    </a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // Auto-submit form on filter change
    document.querySelectorAll('select[name="status"], select[name="category"], select[name="type"]').forEach(function(select) {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });
</script>
@endpush
