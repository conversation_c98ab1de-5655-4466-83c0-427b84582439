@extends('layouts.frontend')

@section('content')
<!-- Hero Section -->
<section class="bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3">Stay Informed with Ghana's Premier Legal News</h1>
                <p class="lead mb-4">Get the latest legal developments, case law analysis, and professional insights from Ghana's top legal experts.</p>
                <div class="d-flex flex-wrap gap-3">
                    <a href="{{ route('articles.index') }}" class="btn btn-light btn-lg">
                        <i class="fas fa-newspaper me-2"></i>Browse Articles
                    </a>
                    <a href="{{ route('subscriptions.index') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-crown me-2"></i>Go Premium
                    </a>
                </div>
            </div>
            <div class="col-lg-4 text-center">
                <i class="fas fa-balance-scale" style="font-size: 8rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Breaking News -->
@if($breakingNews->count() > 0)
<section class="py-3 bg-danger text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-auto">
                <span class="breaking-news">
                    <i class="fas fa-bolt me-2"></i>BREAKING NEWS
                </span>
            </div>
            <div class="col">
                <div id="breakingNewsCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        @foreach($breakingNews as $index => $news)
                            <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                                <a href="{{ route('articles.show', $news->slug) }}" class="text-white text-decoration-none">
                                    <strong>{{ $news->title }}</strong>
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endif

<div class="container my-5">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Featured Articles -->
            @if($featuredArticles->count() > 0)
                <section class="mb-5">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="h3 mb-0">Featured Articles</h2>
                        <a href="{{ route('articles.index') }}" class="btn btn-outline-primary">View All</a>
                    </div>
                    
                    <div class="row">
                        @foreach($featuredArticles->take(3) as $index => $article)
                            <div class="col-md-{{ $index === 0 ? '12' : '6' }} mb-4">
                                <div class="card article-card h-100">
                                    @if($article->featured_image)
                                        <img src="{{ $article->featured_image }}" class="card-img-top" alt="{{ $article->title }}">
                                    @endif
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <span class="category-badge text-white" style="background-color: {{ $article->category->color }}">
                                                {{ $article->category->name }}
                                            </span>
                                            @if($article->is_premium)
                                                <span class="premium-badge">Premium</span>
                                            @endif
                                        </div>
                                        <h5 class="card-title">
                                            <a href="{{ route('articles.show', $article->slug) }}" class="text-decoration-none text-dark">
                                                {{ $article->title }}
                                            </a>
                                        </h5>
                                        <p class="card-text text-muted">{{ Str::limit($article->excerpt, 120) }}</p>
                                        <div class="d-flex justify-content-between align-items-center text-muted small">
                                            <span>
                                                <i class="fas fa-user me-1"></i>{{ $article->author->name }}
                                            </span>
                                            <span>
                                                <i class="fas fa-calendar me-1"></i>{{ $article->published_at->format('M j, Y') }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </section>
            @endif

            <!-- Content Ad -->
            @if(isset($contentAds) && $contentAds->count() > 0)
                @foreach($contentAds as $ad)
                    <div class="ad-container my-4">
                        <div class="ad-label">Advertisement</div>
                        <a href="{{ route('ad.click', $ad->id) }}" target="_blank" rel="noopener">
                            @if($ad->image_url)
                                <img src="{{ $ad->image_url }}" alt="{{ $ad->title }}" class="img-fluid">
                            @else
                                <div class="p-3">
                                    <h6 class="mb-1">{{ $ad->title }}</h6>
                                    <p class="mb-0 text-muted">{{ $ad->description }}</p>
                                </div>
                            @endif
                        </a>
                    </div>
                @endforeach
            @endif

            <!-- Categories with Articles -->
            @if($categorizedArticles->count() > 0)
                <section class="mb-5">
                    <h2 class="h3 mb-4">Latest by Category</h2>
                    
                    @foreach($categorizedArticles as $category)
                        @if($category->publishedArticles->count() > 0)
                            <div class="mb-5">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h4 class="h5 mb-0">
                                        <a href="{{ route('categories.show', $category->slug) }}" class="text-decoration-none" style="color: {{ $category->color }}">
                                            <i class="{{ $category->icon ?? 'fas fa-folder' }} me-2"></i>
                                            {{ $category->name }}
                                        </a>
                                    </h4>
                                    <a href="{{ route('categories.show', $category->slug) }}" class="btn btn-sm btn-outline-primary">View All</a>
                                </div>
                                
                                <div class="row">
                                    @foreach($category->publishedArticles->take(2) as $article)
                                        <div class="col-md-6 mb-3">
                                            <div class="card article-card h-100">
                                                @if($article->featured_image)
                                                    <img src="{{ $article->featured_image }}" class="card-img-top" alt="{{ $article->title }}" style="height: 150px;">
                                                @endif
                                                <div class="card-body">
                                                    @if($article->is_premium)
                                                        <span class="premium-badge mb-2 d-inline-block">Premium</span>
                                                    @endif
                                                    <h6 class="card-title">
                                                        <a href="{{ route('articles.show', $article->slug) }}" class="text-decoration-none text-dark">
                                                            {{ Str::limit($article->title, 60) }}
                                                        </a>
                                                    </h6>
                                                    <p class="card-text text-muted small">{{ Str::limit($article->excerpt, 80) }}</p>
                                                    <div class="text-muted small">
                                                        <i class="fas fa-user me-1"></i>{{ $article->author->name }}
                                                        <span class="ms-2">
                                                            <i class="fas fa-calendar me-1"></i>{{ $article->published_at->format('M j') }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    @endforeach
                </section>
            @endif

            <!-- Recent Articles -->
            @if($recentArticles->count() > 0)
                <section class="mb-5">
                    <h2 class="h3 mb-4">Recent Articles</h2>
                    
                    <div class="row">
                        @foreach($recentArticles->take(6) as $article)
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card article-card h-100">
                                    @if($article->featured_image)
                                        <img src="{{ $article->featured_image }}" class="card-img-top" alt="{{ $article->title }}">
                                    @endif
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <span class="category-badge text-white small" style="background-color: {{ $article->category->color }}">
                                                {{ $article->category->name }}
                                            </span>
                                            @if($article->is_premium)
                                                <span class="premium-badge">Premium</span>
                                            @endif
                                        </div>
                                        <h6 class="card-title">
                                            <a href="{{ route('articles.show', $article->slug) }}" class="text-decoration-none text-dark">
                                                {{ Str::limit($article->title, 60) }}
                                            </a>
                                        </h6>
                                        <p class="card-text text-muted small">{{ Str::limit($article->excerpt, 80) }}</p>
                                        <div class="text-muted small">
                                            <i class="fas fa-calendar me-1"></i>{{ $article->published_at->format('M j, Y') }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </section>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Subscription CTA -->
            @guest
                <div class="subscription-cta mb-4">
                    <h5 class="mb-3">
                        <i class="fas fa-crown me-2"></i>
                        Unlock Premium Content
                    </h5>
                    <p class="mb-3">Get unlimited access to in-depth legal analysis, case law database, and exclusive content.</p>
                    <a href="{{ route('subscriptions.index') }}" class="btn btn-light btn-sm">
                        Start Free Trial
                    </a>
                </div>
            @endguest

            <!-- Sidebar Ads -->
            @if(isset($sidebarAds) && $sidebarAds->count() > 0)
                @foreach($sidebarAds as $ad)
                    <div class="ad-container mb-4">
                        <div class="ad-label">Advertisement</div>
                        <a href="{{ route('ad.click', $ad->id) }}" target="_blank" rel="noopener">
                            @if($ad->image_url)
                                <img src="{{ $ad->image_url }}" alt="{{ $ad->title }}" class="img-fluid">
                            @else
                                <div class="p-3">
                                    <h6 class="mb-1">{{ $ad->title }}</h6>
                                    <p class="mb-0 text-muted">{{ $ad->description }}</p>
                                </div>
                            @endif
                        </a>
                    </div>
                @endforeach
            @endif

            <!-- Most Read Articles -->
            @if($mostReadArticles->count() > 0)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-fire me-2 text-danger"></i>
                            Most Read
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        @foreach($mostReadArticles as $index => $article)
                            <div class="d-flex p-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                                <div class="flex-shrink-0 me-3">
                                    <span class="badge bg-primary rounded-pill">{{ $index + 1 }}</span>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="{{ route('articles.show', $article->slug) }}" class="text-decoration-none text-dark">
                                            {{ Str::limit($article->title, 60) }}
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>{{ number_format($article->views_count) }} views
                                    </small>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Newsletter Signup -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-envelope me-2"></i>
                        Legal Newsletter
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Get weekly updates on the latest legal developments in Ghana.</p>
                    <form>
                        <div class="mb-3">
                            <input type="email" class="form-control" placeholder="Your email address" required>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">Subscribe</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
